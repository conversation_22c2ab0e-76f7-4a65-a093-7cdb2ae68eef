name: New language request
description: Suggest a new language to add to Crowdin platform for further translation.
title: '[Enter your language here] - New language request'
labels: [i18n]
body:
  - type: markdown
    attributes:
      value: |
        Add an issue title in this format: [English] - New language request
  - type: input
    attributes:
      label: 'Enter the jw.org url to access this language:'
      placeholder: |
        jw.org/en/
    validations:
      required: true
  - type: markdown
    attributes:
      value: |
        We can provide AI-generated pre-translations if you send us your language's translation list. This way, you can focus on proofreading rather than starting from scratch. While the pre-translations may not be perfect, many strings will already be accurately translated, saving you hours of work.
  - type: textarea
    attributes:
      label: 'Please provide translations for the following theocratic terms in your language:'
      value: |
        Assignment: 
        Student (of a meeting): 
        Assistant (of a student): 
        Bible study:
        Branch office: 
        Bethel: 
        Chairman: 
        Congregation: 
        Circuit overseer: 
        Circuit overseer visit:
        Elder: 
        Governing Body Update (video): 
        Kingdom Hall:
        Ministerial servant: 
        Ministry: 
        Meeting: 
        Meeting schedule: 
        Meeting workbook: 
        Memorial: 
        Midweek meeting: 
        Weekend meeting: 
        Organizational Accomplishments (video): 
        Schedule: 
        Service: 
        Publisher: 
        Prayer: 
        Pioneer application: 
        Report: 
        Reader: 
        Regular pioneer: 
        Auxiliary pioneer: 
        Speaker: 
        Talk: 
        Territory:
    validations:
      required: true
  - type: input
    attributes:
      label: 'Are you fluent to proofread the translations? If yes, provide your Crowdin username:'
      placeholder: |
        Yes, my Crowdin username is...
    validations:
      required: false
