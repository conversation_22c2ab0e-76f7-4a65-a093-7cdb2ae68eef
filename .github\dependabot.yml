version: 2
updates:
  # Maintain dependencies for GitHub Actions
  - package-ecosystem: 'github-actions'
    directory: '/'
    schedule:
      interval: 'daily'
    commit-message:
      prefix: 'chore(actions)'

  # Maintain dependencies for npm
  - package-ecosystem: 'npm'
    directory: '/'
    schedule:
      interval: 'daily'
    allow:
      - dependency-type: 'production'
      - dependency-type: 'development'
    commit-message:
      prefix: 'deps(apps)'
      prefix-development: 'deps(dev)'
    groups:
      mui:
        patterns:
          - '@mui*'
