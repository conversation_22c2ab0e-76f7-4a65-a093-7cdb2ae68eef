name: CI

on:
  push:
    branches: [main]
  workflow_dispatch:

permissions: read-all

jobs:
  cypress-ci:
    name: Cypress CI
    runs-on: ubuntu-latest

    permissions:
      actions: read
      contents: read

    steps:
      - name: Checkout for testing
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          ref: main
          persist-credentials: false

      - name: Use Node.js LTS version
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020
        with:
          node-version: lts/Jod

      - name: Install Dependencies
        run: npm ci && npm i -D @rollup/rollup-linux-x64-gnu

      - name: Cypress run testing
        uses: cypress-io/github-action@b8ba51a856ba5f4c15cf39007636d4ab04f23e3c
        with:
          command-prefix: '--'
          install: false
          build: npm run build
          start: npm run preview
          browser: chrome
          record: true
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CYPRESS_PROJECT_ID: vwwffz
          VITE_FIREBASE_APIKEY: ${{ secrets.VITE_FIREBASE_APIKEY }}
          VITE_FIREBASE_APPID: ${{ secrets.VITE_FIREBASE_APPID }}
          VITE_FIREBASE_AUTHDOMAIN: ${{ secrets.VITE_FIREBASE_AUTHDOMAIN }}
          VITE_FIREBASE_MEASUREMENTID: ${{ secrets.VITE_FIREBASE_MEASUREMENTID }}
          VITE_FIREBASE_MESSAGINGSENDERID: ${{ secrets.VITE_FIREBASE_MESSAGINGSENDERID }}
          VITE_FIREBASE_PROJECTID: ${{ vars.VITE_FIREBASE_PROJECTID }}
