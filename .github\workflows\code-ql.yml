name: CodeQL

on:
  push:
    branches: [main]
  pull_request:
  schedule:
    - cron: '0 12 * * 4'

permissions: read-all

jobs:
  codeql:
    name: Code QL
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    steps:
      - name: Checkout repository for code analysis
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

      - name: Initialize CodeQL
        uses: github/codeql-action/init@51f77329afa6477de8c49fc9c7046c15b9a4e79d
        with:
          languages: javascript
          queries: security-extended

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@51f77329afa6477de8c49fc9c7046c15b9a4e79d
