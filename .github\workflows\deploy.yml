name: Deploy app to Live
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  workflow_dispatch:

permissions: read-all

jobs:
  dev_deploy:
    name: Build and Deploy to Production
    if: ${{ github.repository == 'sws2apps/organized-app' && github.ref == 'refs/heads/main' }}
    environment:
      name: Live (organized-app.com)
      url: https://organized-app.com
    runs-on: ubuntu-latest

    permissions:
      contents: 'read'
      id-token: 'write'

    steps:
      - name: Checkout for release preparation
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          ref: main
          persist-credentials: false

      - name: Use Node.js LTS version
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020
        with:
          node-version: lts/Jod

      - name: Install package dependencies
        run: npm ci

      - name: Run Semantic Release to version and build App
        run: npx semantic-release
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Deploy project to Vercel
        run: vercel deploy --prod --token=${{ secrets.VERCEL_TOKEN }}
