name: NodeJSScan

on:
  schedule:
    - cron: '0 12 * * 4'

permissions: read-all

jobs:
  nodejsscan:
    name: <PERSON><PERSON><PERSON><PERSON>
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      actions: read
      contents: read

    steps:
      - name: Checkout the code
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683

      - name: Setup Python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065
        with:
          python-version: '3.12'

      - name: nodejsscan scan
        id: njsscan
        uses: ajinabraham/njsscan-action@231750a435d85095d33be7d192d52ec650625146
        with:
          args: '. --sarif --output results.sarif || true'

      - name: Upload njsscan report
        uses: github/codeql-action/upload-sarif@51f77329afa6477de8c49fc9c7046c15b9a4e79d
        with:
          sarif_file: results.sarif
