name: Scorecards

on:
  branch_protection_rule:
  schedule:
    - cron: '0 12 * * 4'
  push:
    branches: [main]

permissions: read-all

jobs:
  scorecards:
    name: Scorecards Analysis
    runs-on: ubuntu-latest
    permissions:
      # Needed to upload the results to code-scanning dashboard.
      security-events: write
      # Used to receive a badge. (Upcoming feature)
      id-token: write
      actions: read
      contents: read

    steps:
      - name: 'Checkout code'
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          persist-credentials: false

      - name: 'Run analysis'
        uses: ossf/scorecard-action@05b42c624433fc40578a4040d5cf5e36ddca8cde
        with:
          results_file: results.sarif
          results_format: sarif
          repo_token: ${{ secrets.SCORECARD_READ_TOKEN }}
          publish_results: true

      - name: 'Upload to code-scanning'
        uses: github/codeql-action/upload-sarif@51f77329afa6477de8c49fc9c7046c15b9a4e79d
        with:
          sarif_file: results.sarif
