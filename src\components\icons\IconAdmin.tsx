import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconAdmin = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-admin ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_3109_69418"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_3109_69418)">
          <path
            d="M12 12.7505C11.0872 12.7505 10.3173 12.4371 9.69037 11.8102C9.06346 11.1832 8.75 10.4134 8.75 9.50056C8.75 8.58775 9.06346 7.81788 9.69037 7.19096C10.3173 6.56405 11.0872 6.25059 12 6.25059C12.9128 6.25059 13.6827 6.56405 14.3096 7.19096C14.9365 7.81788 15.25 8.58775 15.25 9.50056C15.25 10.4134 14.9365 11.1832 14.3096 11.8102C13.6827 12.4371 12.9128 12.7505 12 12.7505ZM12 11.2506C12.4974 11.2506 12.9134 11.0833 13.2481 10.7487C13.5827 10.414 13.75 9.998 13.75 9.50056C13.75 9.00313 13.5827 8.5871 13.2481 8.25246C12.9134 7.91785 12.4974 7.75054 12 7.75054C11.5025 7.75054 11.0865 7.91785 10.7519 8.25246C10.4173 8.5871 10.2499 9.00313 10.2499 9.50056C10.2499 9.998 10.4173 10.414 10.7519 10.7487C11.0865 11.0833 11.5025 11.2506 12 11.2506ZM12 21.4813C9.83716 20.8916 8.04646 19.6185 6.62787 17.6621C5.20929 15.7057 4.5 13.5185 4.5 11.1006V5.34674L12 2.53906L19.5 5.34674V11.1006C19.5 13.5185 18.7907 15.7057 17.3721 17.6621C15.9535 19.6185 14.1628 20.8916 12 21.4813ZM12 4.13519L5.99997 6.37556V11.1006C5.99997 12.0518 6.13619 12.9717 6.40863 13.8602C6.68108 14.7487 7.05961 15.5775 7.54422 16.3467C8.21857 16.0031 8.92466 15.7345 9.66248 15.541C10.4003 15.3474 11.1795 15.2506 12 15.2506C12.8205 15.2506 13.5997 15.3474 14.3375 15.541C15.0753 15.7345 15.7814 16.0031 16.4557 16.3467C16.9403 15.5775 17.3189 14.7487 17.5913 13.8602C17.8638 12.9717 18 12.0518 18 11.1006V6.37556L12 4.13519ZM12 16.7505C11.3551 16.7505 10.7349 16.8204 10.1394 16.9602C9.54388 17.0999 8.98137 17.2961 8.45187 17.5486C8.94804 18.0999 9.49388 18.5765 10.0894 18.9784C10.6849 19.3804 11.3218 19.6877 12 19.9006C12.6782 19.6877 13.3134 19.3804 13.9058 18.9784C14.4981 18.5765 15.0423 18.0999 15.5385 17.5486C15.009 17.2961 14.4481 17.0999 13.8558 16.9602C13.2634 16.8204 12.6449 16.7505 12 16.7505Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconAdmin;
