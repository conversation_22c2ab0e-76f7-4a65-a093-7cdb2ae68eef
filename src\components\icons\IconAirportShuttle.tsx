import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconAirportShuttle = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-airport-shuttle ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_13161_343533"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="24"
        >
          <rect width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_13161_343533)">
          <path
            d="M6.18275 18.096C5.46158 18.096 4.84858 17.8437 4.34375 17.339C3.83892 16.8342 3.5865 16.2212 3.5865 15.5H1.625V7C1.625 6.5875 1.77192 6.23442 2.06575 5.94075C2.35942 5.64692 2.7125 5.5 3.125 5.5H16.6635L22.375 11.0578V15.5H20.5673C20.5673 16.2212 20.3148 16.8342 19.81 17.339C19.3052 17.8437 18.6923 18.096 17.9713 18.096C17.2501 18.096 16.6371 17.8437 16.1322 17.339C15.6274 16.8342 15.375 16.2212 15.375 15.5H8.77875C8.77875 16.2178 8.52642 16.83 8.02175 17.3365C7.51692 17.8428 6.90392 18.096 6.18275 18.096ZM14.625 10.2885H19.3557L15.952 7H14.625V10.2885ZM8.875 10.2885H13.125V7H8.875V10.2885ZM3.125 10.2885H7.375V7H3.125V10.2885ZM6.18275 16.75C6.53275 16.75 6.82858 16.6292 7.07025 16.3875C7.31192 16.1458 7.43275 15.85 7.43275 15.5C7.43275 15.15 7.31192 14.8542 7.07025 14.6125C6.82858 14.3708 6.53275 14.25 6.18275 14.25C5.83275 14.25 5.53692 14.3708 5.29525 14.6125C5.05358 14.8542 4.93275 15.15 4.93275 15.5C4.93275 15.85 5.05358 16.1458 5.29525 16.3875C5.53692 16.6292 5.83275 16.75 6.18275 16.75ZM17.9713 16.75C18.3213 16.75 18.6171 16.6292 18.8587 16.3875C19.1004 16.1458 19.2213 15.85 19.2213 15.5C19.2213 15.15 19.1004 14.8542 18.8587 14.6125C18.6171 14.3708 18.3213 14.25 17.9713 14.25C17.6213 14.25 17.3254 14.3708 17.0837 14.6125C16.8421 14.8542 16.7212 15.15 16.7212 15.5C16.7212 15.85 16.8421 16.1458 17.0837 16.3875C17.3254 16.6292 17.6213 16.75 17.9713 16.75ZM3.125 14H4.11725C4.33008 13.7 4.61508 13.4423 4.97225 13.227C5.32925 13.0115 5.73275 12.9037 6.18275 12.9037C6.63275 12.9037 7.03142 13.0067 7.37875 13.2125C7.72625 13.4183 8.016 13.6808 8.248 14H15.9057C16.1186 13.7 16.4035 13.4423 16.7605 13.227C17.1177 13.0115 17.5213 12.9037 17.9713 12.9037C18.4213 12.9037 18.8199 13.0067 19.1672 13.2125C19.5147 13.4183 19.8045 13.6808 20.0365 14H20.875V11.7885H3.125V14Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconAirportShuttle;
