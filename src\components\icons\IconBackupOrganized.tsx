import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconBackupOrganized = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-backup-organized ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_11573_297345"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="24"
        >
          <rect width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_11573_297345)">
          <path
            d="M6.30775 21.5C5.80258 21.5 5.375 21.325 5.025 20.975C4.675 20.625 4.5 20.1974 4.5 19.6923V4.30775C4.5 3.80258 4.675 3.375 5.025 3.025C5.375 2.675 5.80258 2.5 6.30775 2.5H14.25L19.5 7.75V19.6923C19.5 20.1974 19.325 20.625 18.975 20.975C18.625 21.325 18.1974 21.5 17.6923 21.5H6.30775ZM13.5 8.5V4H6.30775C6.23075 4 6.16025 4.03208 6.09625 4.09625C6.03208 4.16025 6 4.23075 6 4.30775V19.6923C6 19.7693 6.03208 19.8398 6.09625 19.9038C6.16025 19.9679 6.23075 20 6.30775 20H17.6923C17.7692 20 17.8398 19.9679 17.9038 19.9038C17.9679 19.8398 18 19.7693 18 19.6923V8.5H13.5Z"
            fill={color}
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10.7846 12.4635H10.7878V11.6237C10.7878 11.0677 11.1517 10.5772 11.6838 10.416L9.99741 10.416C9.2099 10.416 8.12921 11.0919 8.04918 12.3176C8.04604 12.3555 8.04443 12.3938 8.04443 12.4324L8.04443 13.4528C8.04443 14.2095 8.65784 14.8229 9.41452 14.8229C10.1712 14.8229 10.7846 14.2095 10.7846 13.4528V12.4635ZM13.9072 13.1563V13.1563H12.9263C12.1696 13.1563 11.5562 12.5429 11.5562 11.7862C11.5562 11.0295 12.1696 10.4161 12.9263 10.4161H13.9383C13.977 10.4161 14.0153 10.4177 14.0532 10.4209C15.2789 10.5009 15.9547 11.5816 15.9547 12.3691V14.0555C15.7954 13.5219 15.3047 13.1563 14.7478 13.1563H13.9072ZM10.0872 15.5833L10.0872 15.5863H11.1513C11.908 15.5863 12.5214 16.1997 12.5214 16.9564C12.5214 17.713 11.908 18.3264 11.1513 18.3264H10.092L10.092 18.327C8.77411 18.327 8.04446 17.1923 8.04446 16.3741V14.6877C8.20082 15.2187 8.68823 15.5833 9.24177 15.5833H10.0872ZM13.2135 16.2831L13.2146 16.2831V15.2481C13.2146 14.4914 13.828 13.878 14.5847 13.878C15.3414 13.878 15.9548 14.4914 15.9548 15.2481V16.2789L15.9553 16.2789C15.9553 17.5968 14.8206 18.3264 14.0024 18.3264H12.316C12.8482 18.1697 13.2135 17.6813 13.2135 17.1265V16.2831Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconBackupOrganized;
