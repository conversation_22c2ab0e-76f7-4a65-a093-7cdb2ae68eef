import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconCalendarClock = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-calendar_clock ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="25"
        viewBox="0 0 24 25"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_13644_59310"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.5" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_13644_59310)">
          <path
            d="M4.99997 9.30591H19V6.80589C19 6.72896 18.9679 6.65843 18.9038 6.59431C18.8397 6.53021 18.7692 6.49816 18.6922 6.49816H5.3077C5.23077 6.49816 5.16024 6.53021 5.09612 6.59431C5.03202 6.65843 4.99997 6.72896 4.99997 6.80589V9.30591ZM5.3077 21.9981C4.80257 21.9981 4.375 21.8231 4.025 21.4731C3.675 21.1231 3.5 20.6956 3.5 20.1904V6.80589C3.5 6.30076 3.675 5.87319 4.025 5.52319C4.375 5.17319 4.80257 4.99819 5.3077 4.99819H6.69232V2.88281H8.23075V4.99819H15.8077V2.88281H17.3076V4.99819H18.6922C19.1974 4.99819 19.625 5.17319 19.975 5.52319C20.325 5.87319 20.5 6.30076 20.5 6.80589V12.2693C20.2602 12.1642 20.0153 12.0793 19.7654 12.0145C19.5154 11.9498 19.2602 11.8988 19 11.8616V10.8059H4.99997V20.1904C4.99997 20.2674 5.03202 20.3379 5.09612 20.402C5.16024 20.4661 5.23077 20.4982 5.3077 20.4982H11.8096C11.8942 20.7751 11.9965 21.0369 12.1164 21.2837C12.2362 21.5305 12.3724 21.7687 12.525 21.9981H5.3077ZM18.1923 22.9981C16.9436 22.9981 15.8814 22.5603 15.0058 21.6847C14.1301 20.809 13.6923 19.7469 13.6923 18.4982C13.6923 17.2495 14.1301 16.1873 15.0058 15.3116C15.8814 14.436 16.9436 13.9982 18.1923 13.9982C19.441 13.9982 20.5032 14.436 21.3788 15.3116C22.2544 16.1873 22.6922 17.2495 22.6922 18.4982C22.6922 19.7469 22.2544 20.809 21.3788 21.6847C20.5032 22.5603 19.441 22.9981 18.1923 22.9981ZM19.8577 20.7866L20.4807 20.1635L18.6346 18.3174V15.5559H17.75V18.6789L19.8577 20.7866Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconCalendarClock;
