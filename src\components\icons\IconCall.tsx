import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconCall = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-call ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_3196_82422"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_3196_82422)">
          <path
            d="M19.4403 20.5004C17.5557 20.5004 15.6625 20.0623 13.7606 19.186C11.8586 18.3097 10.1112 17.0735 8.51828 15.4774C6.92533 13.8812 5.69071 12.1338 4.81442 10.2351C3.93814 8.33637 3.5 6.44471 3.5 4.56011C3.5 4.25736 3.6 4.00507 3.8 3.80324C4 3.60141 4.25 3.50049 4.55 3.50049H7.8115C8.06407 3.50049 8.28683 3.58286 8.47977 3.74761C8.67272 3.91236 8.79548 4.11589 8.84803 4.35819L9.4211 7.30046C9.46085 7.57355 9.45252 7.80816 9.3961 8.00431C9.3397 8.20046 9.23842 8.36521 9.09225 8.49854L6.78265 10.7466C7.15445 11.4274 7.57913 12.0713 8.0567 12.6783C8.53427 13.2854 9.05125 13.8652 9.60765 14.4178C10.1564 14.9665 10.7397 15.4761 11.3577 15.9466C11.9756 16.4172 12.6429 16.855 13.3596 17.2601L15.6038 14.9966C15.7602 14.8338 15.9497 14.7197 16.1721 14.6543C16.3945 14.589 16.6256 14.5729 16.8654 14.6063L19.6423 15.1716C19.8948 15.2383 20.1009 15.3671 20.2605 15.5582C20.4201 15.7492 20.5 15.9659 20.5 16.2082V19.4504C20.5 19.7504 20.399 20.0004 20.1972 20.2004C19.9954 20.4004 19.7431 20.5004 19.4403 20.5004ZM6.07305 9.32741L7.85768 7.61971C7.88973 7.59406 7.91056 7.55881 7.92018 7.51394C7.92979 7.46906 7.92819 7.42739 7.91538 7.38894L7.48075 5.15431C7.46793 5.10303 7.4455 5.06457 7.41345 5.03894C7.3814 5.01329 7.33973 5.00046 7.28845 5.00046H5.14997C5.11152 5.00046 5.07948 5.01329 5.05383 5.03894C5.02818 5.06457 5.01535 5.09662 5.01535 5.13509C5.06663 5.81842 5.17849 6.51266 5.35092 7.21779C5.52337 7.92292 5.76408 8.62613 6.07305 9.32741ZM14.773 17.9697C15.4359 18.2787 16.1272 18.5149 16.8471 18.6784C17.567 18.8418 18.2397 18.9389 18.8654 18.9697C18.9038 18.9697 18.9359 18.9569 18.9615 18.9312C18.9872 18.9056 19 18.8735 19 18.8351V16.7312C19 16.68 18.9872 16.6383 18.9615 16.6062C18.9359 16.5742 18.8974 16.5517 18.8461 16.5389L16.7461 16.112C16.7077 16.0992 16.674 16.0976 16.6452 16.1072C16.6163 16.1168 16.5859 16.1376 16.5538 16.1697L14.773 17.9697Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconCall;
