import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconClean = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-clean ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_3258_161977"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_3258_161977)">
          <path
            d="M10.846 11.2505H13.1537V4.09667C13.1537 3.77489 13.0418 3.50213 12.8181 3.2784C12.5944 3.05468 12.3216 2.94282 11.9998 2.94282C11.678 2.94282 11.4053 3.05468 11.1816 3.2784C10.9578 3.50213 10.846 3.77489 10.846 4.09667V11.2505ZM4.94212 15.0582H19.0575V13.0582C19.0575 12.9685 19.0287 12.8947 18.971 12.837C18.9133 12.7793 18.8396 12.7505 18.7498 12.7505H5.24985C5.1601 12.7505 5.08637 12.7793 5.02867 12.837C4.97097 12.8947 4.94212 12.9685 4.94212 13.0582V15.0582ZM3.81907 21.0582H6.346V18.8082C6.346 18.5957 6.41791 18.4176 6.56172 18.2738C6.70552 18.1301 6.88372 18.0582 7.09632 18.0582C7.30891 18.0582 7.487 18.1301 7.6306 18.2738C7.77418 18.4176 7.84597 18.5957 7.84597 18.8082V21.0582H11.2499V18.8082C11.2499 18.5957 11.3217 18.4176 11.4655 18.2738C11.6094 18.1301 11.7876 18.0582 12.0001 18.0582C12.2127 18.0582 12.3908 18.1301 12.5344 18.2738C12.678 18.4176 12.7498 18.5957 12.7498 18.8082V21.0582H16.1537V18.8082C16.1537 18.5957 16.2256 18.4176 16.3694 18.2738C16.5132 18.1301 16.6914 18.0582 16.904 18.0582C17.1166 18.0582 17.2947 18.1301 17.4383 18.2738C17.5819 18.4176 17.6537 18.5957 17.6537 18.8082V21.0582H20.1806C20.2831 21.0582 20.3665 21.0182 20.4306 20.938C20.4947 20.8579 20.5107 20.7697 20.4787 20.6736L19.3633 16.5582H4.63637L3.52097 20.6736C3.48892 20.7697 3.50495 20.8579 3.56905 20.938C3.63317 21.0182 3.71651 21.0582 3.81907 21.0582ZM20.2575 22.5582H3.74215C3.17548 22.5582 2.71459 22.3319 2.35947 21.8793C2.00434 21.4268 1.9037 20.9223 2.05755 20.3659L3.44217 15.2217V13.0005C3.44217 12.5146 3.61236 12.1015 3.95272 11.7611C4.29311 11.4207 4.70625 11.2505 5.19215 11.2505H9.346V4.09667C9.346 3.35951 9.60401 2.73291 10.12 2.2169C10.6361 1.70088 11.2627 1.44287 11.9998 1.44287C12.737 1.44287 13.3636 1.70088 13.8796 2.2169C14.3956 2.73291 14.6537 3.35951 14.6537 4.09667V11.2505H18.8075C19.2934 11.2505 19.7065 11.4207 20.0469 11.7611C20.3873 12.1015 20.5575 12.5146 20.5575 13.0005V15.2217L21.9421 20.3851C22.1267 20.9351 22.0389 21.4348 21.6786 21.8841C21.3184 22.3335 20.8447 22.5582 20.2575 22.5582Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconClean;
