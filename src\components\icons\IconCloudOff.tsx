import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconCloudOff = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-cloud_off ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_2621_40480"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_2621_40480)">
          <path
            d="M20.1269 22.235L17.3808 19.5004H6.49998C5.10127 19.5004 3.91827 19.0168 2.95095 18.0495C1.98365 17.0821 1.5 15.8991 1.5 14.5004C1.5 13.2491 1.90225 12.162 2.70675 11.2389C3.51123 10.3158 4.50001 9.76711 5.67308 9.59276C5.70384 9.40172 5.76346 9.18281 5.85193 8.93601C5.94039 8.68919 6.03848 8.46194 6.14618 8.25426L2.1308 4.23888L3.18463 3.18506L21.1808 21.1812L20.1269 22.235ZM6.49998 18.0004H15.8616L7.2923 9.43113C7.20127 9.65935 7.12979 9.90808 7.07788 10.1773C7.02594 10.4465 6.99998 10.7209 6.99998 11.0004H6.49998C5.53331 11.0004 4.70831 11.3421 4.02498 12.0254C3.34164 12.7087 2.99998 13.5337 2.99998 14.5004C2.99998 15.4671 3.34164 16.2921 4.02498 16.9754C4.70831 17.6587 5.53331 18.0004 6.49998 18.0004ZM21.2538 18.4042L20.1692 17.3504C20.4397 17.1107 20.6458 16.8376 20.7875 16.5313C20.9291 16.2249 21 15.8813 21 15.5004C21 14.8004 20.7583 14.2087 20.275 13.7254C19.7916 13.2421 19.2 13.0004 18.5 13.0004H17V11.0004C17 9.61708 16.5125 8.43791 15.5375 7.46291C14.5625 6.48791 13.3833 6.00041 12 6.00041C11.55 6.00041 11.1166 6.05458 10.7 6.16291C10.2833 6.27124 9.88331 6.44208 9.49998 6.67541L8.41538 5.58121C8.99871 5.20684 9.57788 4.93313 10.1529 4.76006C10.7279 4.58698 11.3436 4.50043 12 4.50043C13.8107 4.50043 15.3467 5.13109 16.608 6.39241C17.8693 7.65371 18.5 9.18971 18.5 11.0004V11.5004H18.8077C19.8615 11.5825 20.7403 12.0062 21.4442 12.7716C22.148 13.537 22.5 14.4466 22.5 15.5004C22.5 16.0542 22.3958 16.5783 22.1875 17.0725C21.9791 17.5667 21.6679 18.0106 21.2538 18.4042Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconCloudOff;
