import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconCollaborativeActivities = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-collaborative-activities ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_4683_164277"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_4683_164277)">
          <path
            d="M5.63453 12.189L2.21533 8.76983L5.63453 5.36025L9.04411 8.76983L5.63453 12.189ZM9.40378 21.5006V16.7025C8.40635 16.6191 7.41565 16.5047 6.43168 16.3592C5.4477 16.2137 4.47045 16.014 3.49993 15.7602L3.86531 14.2602C5.20762 14.6115 6.55215 14.8564 7.89888 14.9948C9.24562 15.1333 10.6123 15.2025 11.9989 15.2025C13.3855 15.2025 14.7525 15.1333 16.0999 14.9948C17.4473 14.8564 18.7922 14.6115 20.1345 14.2602L20.4999 15.7602C19.5294 16.014 18.5521 16.2137 17.5681 16.3592C16.5842 16.5047 15.5935 16.6191 14.596 16.7025V21.5006H9.40378ZM5.63453 10.091L6.94608 8.76983L5.63453 7.45828L4.31336 8.76983L5.63453 10.091ZM11.9999 7.1929C11.2627 7.1929 10.6361 6.9349 10.1201 6.41888C9.60409 5.90285 9.34608 5.27624 9.34608 4.53908C9.34608 3.80191 9.60409 3.17531 10.1201 2.65928C10.6361 2.14326 11.2627 1.88525 11.9999 1.88525C12.7371 1.88525 13.3637 2.14326 13.8797 2.65928C14.3957 3.17531 14.6537 3.80191 14.6537 4.53908C14.6537 5.27624 14.3957 5.90285 13.8797 6.41888C13.3637 6.9349 12.7371 7.1929 11.9999 7.1929ZM11.9999 13.8948C11.4922 13.8948 11.0576 13.714 10.6961 13.3525C10.3345 12.991 10.1538 12.5564 10.1538 12.0487C10.1538 11.541 10.3345 11.1064 10.6961 10.7448C11.0576 10.3833 11.4922 10.2025 11.9999 10.2025C12.5076 10.2025 12.9422 10.3833 13.3038 10.7448C13.6653 11.1064 13.8461 11.541 13.8461 12.0487C13.8461 12.5564 13.6653 12.991 13.3038 13.3525C12.9422 13.714 12.5076 13.8948 11.9999 13.8948ZM11.9999 5.69293C12.3217 5.69293 12.5945 5.58107 12.8182 5.35735C13.0419 5.13364 13.1538 4.86088 13.1538 4.53908C13.1538 4.21728 13.0419 3.94452 12.8182 3.7208C12.5945 3.49709 12.3217 3.38523 11.9999 3.38523C11.6781 3.38523 11.4053 3.49709 11.1816 3.7208C10.9579 3.94452 10.8461 4.21728 10.8461 4.53908C10.8461 4.86088 10.9579 5.13364 11.1816 5.35735C11.4053 5.58107 11.6781 5.69293 11.9999 5.69293ZM17.0211 11.9237L15.5134 9.26983L17.0211 6.616H20.0076L21.5153 9.26983L20.0076 11.9237H17.0211ZM17.8762 10.4237H19.1557L19.7922 9.26983L19.1525 8.11598H17.873L17.2364 9.26983L17.8762 10.4237Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconCollaborativeActivities;
