import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconCompassOn = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-compass-on ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_4944_2979468"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_4944_2979468)">
          <path
            d="M11.9999 19.3163L14.4476 12.0005L11.9999 4.68469L9.55229 12.0005L11.9999 19.3163ZM12.707 12.7076C12.5067 12.9079 12.271 13.0081 11.9999 13.0081C11.7289 13.0081 11.4932 12.9079 11.2928 12.7076C11.0925 12.5072 10.9923 12.2715 10.9923 12.0005C10.9923 11.7294 11.0925 11.4937 11.2928 11.2934C11.4932 11.093 11.7289 10.9929 11.9999 10.9929C12.271 10.9929 12.5067 11.093 12.707 11.2934C12.9074 11.4937 13.0076 11.7294 13.0076 12.0005C13.0076 12.2715 12.9074 12.5072 12.707 12.7076ZM18.7186 18.7168C17.7895 19.6459 16.7399 20.3429 15.5698 20.8078C14.3997 21.2727 13.21 21.5053 12.0008 21.5056C10.7916 21.5058 9.60204 21.2738 8.43212 20.8094C7.26221 20.345 6.21271 19.6483 5.28362 18.7192C4.35453 17.7901 3.65754 16.7405 3.19264 15.5704C2.72773 14.4002 2.49514 13.2106 2.49487 12.0014C2.49459 10.7922 2.72665 9.6026 3.19105 8.43268C3.65543 7.26277 4.35216 6.21327 5.28125 5.28418C6.21035 4.35508 7.25995 3.65809 8.43007 3.19319C9.60021 2.72828 10.7899 2.49569 11.9991 2.49542C13.2083 2.49515 14.3978 2.72721 15.5678 3.1916C16.7377 3.65598 17.7872 4.35272 18.7162 5.28181C19.6453 6.2109 20.3423 7.2605 20.8072 8.43062C21.2721 9.60076 21.5047 10.7904 21.505 11.9996C21.5053 13.2088 21.2732 14.3984 20.8088 15.5683C20.3444 16.7382 19.6477 17.7877 18.7186 18.7168ZM17.6568 17.6573C19.236 16.0781 20.0256 14.1925 20.0256 12.0005C20.0256 9.80846 19.236 7.92284 17.6568 6.34363C16.0776 4.76443 14.192 3.97483 11.9999 3.97483C9.80791 3.97483 7.92229 4.76443 6.34308 6.34363C4.76388 7.92284 3.97427 9.80846 3.97427 12.0005C3.97427 14.1925 4.76388 16.0781 6.34308 17.6573C7.92229 19.2365 9.80791 20.0262 11.9999 20.0262C14.192 20.0262 16.0776 19.2365 17.6568 17.6573Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconCompassOn;
