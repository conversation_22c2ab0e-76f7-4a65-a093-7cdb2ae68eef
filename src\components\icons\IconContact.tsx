import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconContact = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-contact ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_2597_17441"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_2597_17441)">
          <path
            d="M4.25 22.1926V20.6926H19.75V22.1926H4.25ZM4.25 3.30808V1.80811H19.75V3.30808H4.25ZM12 13.5004C12.7628 13.5004 13.4118 13.2328 13.9471 12.6975C14.4823 12.1623 14.75 11.5132 14.75 10.7504C14.75 9.98759 14.4823 9.33856 13.9471 8.80331C13.4118 8.26805 12.7628 8.00043 12 8.00043C11.2372 8.00043 10.5881 8.26805 10.0529 8.80331C9.51763 9.33856 9.25 9.98759 9.25 10.7504C9.25 11.5132 9.51763 12.1623 10.0529 12.6975C10.5881 13.2328 11.2372 13.5004 12 13.5004ZM4.3077 19.5003C3.80257 19.5003 3.375 19.3253 3.025 18.9753C2.675 18.6253 2.5 18.1978 2.5 17.6926V6.30808C2.5 5.80295 2.675 5.37538 3.025 5.02538C3.375 4.67538 3.80257 4.50038 4.3077 4.50038H19.6923C20.1974 4.50038 20.625 4.67538 20.975 5.02538C21.325 5.37538 21.5 5.80295 21.5 6.30808V17.6926C21.5 18.1978 21.325 18.6253 20.975 18.9753C20.625 19.3253 20.1974 19.5003 19.6923 19.5003H4.3077ZM6.40388 18.0004C7.15388 17.1952 8.00131 16.5773 8.94618 16.1465C9.89103 15.7158 10.909 15.5004 12 15.5004C13.091 15.5004 14.1089 15.7158 15.0538 16.1465C15.9986 16.5773 16.8461 17.1952 17.5961 18.0004H19.6923C19.7692 18.0004 19.8397 17.9683 19.9038 17.9042C19.9679 17.8401 20 17.7696 20 17.6926V6.30808C20 6.23115 19.9679 6.16062 19.9038 6.09651C19.8397 6.03241 19.7692 6.00036 19.6923 6.00036H4.3077C4.23077 6.00036 4.16024 6.03241 4.09613 6.09651C4.03202 6.16062 3.99998 6.23115 3.99998 6.30808V17.6926C3.99998 17.7696 4.03202 17.8401 4.09613 17.9042C4.16024 17.9683 4.23077 18.0004 4.3077 18.0004H6.40388ZM8.69998 18.0004H15.3C14.8166 17.667 14.2958 17.417 13.7375 17.2504C13.1791 17.0837 12.6 17.0004 12 17.0004C11.4 17.0004 10.8208 17.0837 10.2625 17.2504C9.70414 17.417 9.18331 17.667 8.69998 18.0004ZM12 12.0004C11.6525 12.0004 11.3573 11.879 11.1144 11.636C10.8714 11.3931 10.75 11.0979 10.75 10.7504C10.75 10.403 10.8714 10.1078 11.1144 9.86483C11.3573 9.62186 11.6525 9.50038 12 9.50038C12.3474 9.50038 12.6426 9.62186 12.8856 9.86483C13.1285 10.1078 13.25 10.403 13.25 10.7504C13.25 11.0979 13.1285 11.3931 12.8856 11.636C12.6426 11.879 12.3474 12.0004 12 12.0004Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconContact;
