import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconFemale = ({
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-female ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="49"
        height="49"
        viewBox="0 0 49 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_2601_22257)">
          <path
            d="M14.8992 34.7011C10.5792 35.6611 9.89921 40.3011 10.0992 42.5011C12.6991 43.9012 19.3388 46.8213 25.0988 47.3013C30.8588 47.7813 36.2988 44.7013 38.2988 43.1013V39.5013C38.2988 37.1013 35.2988 35.9013 34.0988 34.7011C33.1388 33.741 29.6988 33.1012 28.0988 32.9013C27.6988 32.1013 27.1388 30.2613 28.0988 29.3013C29.0588 28.3413 30.0988 26.5013 30.4988 25.7013V19.1011C29.8988 18.3012 28.6988 16.8213 28.6988 17.3013C28.6988 17.7813 27.0988 19.1013 26.2988 19.7013C26.0988 19.5013 25.8188 18.8613 26.2988 17.9013C26.7788 16.9413 26.0988 15.9012 25.6988 15.5012C25.6988 15.1012 25.4588 14.6612 24.4988 16.1012C23.5388 17.5412 20.0988 18.7011 18.4988 19.1011V23.3011C18.4988 27.1411 20.0988 29.3011 20.8988 29.9011V32.3011C20.699 32.7011 19.2192 33.7411 14.8992 34.7011Z"
            fill="#FEFEFE"
          />
          <path
            d="M14.2988 28.1014C14.2988 30.5014 15.8988 32.7014 16.6988 33.5014C19.5788 33.0216 20.6988 32.5013 20.8988 32.3012V29.9012C20.2988 29.3012 18.9788 27.6212 18.4988 25.7012C18.0188 23.7812 18.2988 20.5012 18.4988 19.1012C19.4988 18.7012 21.8588 17.7812 23.2988 17.3012C25.0988 16.7012 25.0988 14.9012 25.6988 16.1012C26.2988 17.3012 25.6988 19.7012 26.2988 19.7012C26.7788 19.7012 28.0988 18.1012 28.6988 17.3012L29.8988 18.5012C30.0988 18.5012 30.4988 19.4612 30.4988 23.3012C30.4988 27.1412 28.8988 28.9012 28.0988 29.3012V32.9012L32.2988 33.5012C32.8988 32.7012 34.2188 30.7412 34.6988 29.3012C35.1788 27.8612 34.8988 21.1012 34.6988 17.9012C34.4988 14.9013 32.2988 8.78141 25.0988 8.30141C17.8988 7.82141 14.8988 14.1014 14.2988 17.3014V28.1014Z"
            fill="#FAA4BE"
          />
          <path
            d="M20.8989 39.5009L22.6989 46.7009H26.8989L28.0989 38.9009C27.4989 39.3009 25.8189 40.1009 23.8989 40.1009C21.9789 40.1009 21.0989 39.7009 20.8989 39.5009Z"
            fill="#FFB7D9"
          />
          <path
            d="M29.2988 32.9009L26.2988 47.3009C30.6188 46.8209 36.4988 43.9009 38.8988 42.5009C38.4988 40.9009 37.4588 37.3409 36.4988 35.9009C35.5388 34.4609 31.2988 33.3009 29.2988 32.9009Z"
            fill="#F0B0C7"
          />
          <path
            d="M19.6987 32.9009L22.6987 47.3009C18.3787 46.8209 12.4987 43.9009 10.0987 42.5009C10.4987 40.9009 11.5387 37.3409 12.4987 35.9009C13.4587 34.4609 17.6987 33.3009 19.6987 32.9009Z"
            fill="#F0B0C7"
          />
          <path
            d="M33.0242 33.4336C33.7427 32.7436 34.3145 31.9156 34.7054 30.9993C35.0963 30.083 35.2981 29.0973 35.2989 28.1011V18.6511C35.2989 15.7867 34.1611 13.0397 32.1357 11.0143C30.1103 8.98893 27.3633 7.85107 24.4989 7.85107C21.6346 7.85107 18.8876 8.98893 16.8622 11.0143C14.8368 13.0397 13.6989 15.7867 13.6989 18.6511V28.1011C13.6997 29.0973 13.9016 30.083 14.2925 30.9993C14.6833 31.9156 15.2552 32.7436 15.9737 33.4336C14.1491 33.9606 12.5453 35.0667 11.4045 36.5851C10.2636 38.1034 9.64741 39.9516 9.64893 41.8508V47.6761C9.64893 47.8551 9.72004 48.0268 9.84663 48.1534C9.97322 48.28 10.1449 48.3511 10.3239 48.3511H38.6739C38.8529 48.3511 39.0246 48.28 39.1512 48.1534C39.2778 48.0268 39.3489 47.8551 39.3489 47.6761V41.8508C39.3504 39.9516 38.7343 38.1034 37.5934 36.5851C36.4525 35.0667 34.8488 33.9606 33.0242 33.4336ZM15.0489 28.1011V18.6511C15.0516 16.1456 16.0481 13.7435 17.8197 11.9719C19.5914 10.2002 21.9935 9.20375 24.4989 9.20107C27.0034 9.20696 29.4036 10.2045 31.1746 11.9754C32.9455 13.7464 33.943 16.1466 33.9489 18.6511V28.1011C33.9481 29.0617 33.7188 30.0083 33.2798 30.8628C32.8408 31.7172 32.2049 32.455 31.4244 33.0151L28.5489 32.2996V30.1126C29.3925 29.416 30.0718 28.5417 30.5386 27.5523C31.0053 26.5628 31.2478 25.4826 31.2489 24.3886V18.6511C31.2489 18.4721 31.1778 18.3004 31.0512 18.1738C30.9246 18.0472 30.753 17.9761 30.5739 17.9761C30.2167 17.9734 29.8749 17.8303 29.6223 17.5777C29.3697 17.3251 29.2266 16.9833 29.2239 16.6261C29.2239 16.4471 29.1528 16.2754 29.0262 16.1488C28.8996 16.0222 28.7279 15.9511 28.5489 15.9511C28.3699 15.9511 28.1982 16.0222 28.0716 16.1488C27.945 16.2754 27.8739 16.4471 27.8739 16.6261C27.8765 17.0017 27.7734 17.3704 27.5765 17.6903C27.3795 18.0101 27.0967 18.2682 26.7602 18.4351C26.9444 17.3164 26.8541 16.1698 26.4969 15.0938C26.4654 14.9802 26.4046 14.8769 26.3207 14.794C26.2367 14.7112 26.1326 14.6518 26.0185 14.6218C25.9045 14.5918 25.7846 14.5921 25.6708 14.6228C25.5569 14.6535 25.4531 14.7135 25.3697 14.7968C23.3177 16.8488 21.4344 17.3281 18.2822 17.9896C18.131 18.0221 17.9955 18.1055 17.8984 18.2259C17.8013 18.3463 17.7486 18.4964 17.7489 18.6511V24.3886C17.75 25.4826 17.9926 26.5628 18.4593 27.5523C18.926 28.5417 19.6054 29.416 20.4489 30.1126V32.2996L17.5734 33.0151C16.793 32.455 16.157 31.7172 15.7181 30.8628C15.2791 30.0083 15.0497 29.0617 15.0489 28.1011ZM28.2249 33.6091L28.3802 33.6496L27.4487 38.7931C26.5443 39.2882 25.5299 39.5477 24.4989 39.5477C23.4679 39.5477 22.4535 39.2882 21.5492 38.7931L20.6177 33.6496L20.7729 33.6091C21.0662 33.5378 21.3269 33.3698 21.513 33.1323C21.6992 32.8947 21.7999 32.6014 21.7989 32.2996V30.9968C21.9204 31.0576 22.0352 31.1183 22.1567 31.1723L22.5819 31.3613C23.1857 31.6284 23.8387 31.7663 24.4989 31.7663C25.1592 31.7663 25.8121 31.6284 26.4159 31.3613L26.8412 31.1723C26.9627 31.1183 27.0774 31.0576 27.1989 30.9968V32.2996C27.198 32.6014 27.2987 32.8947 27.4848 33.1323C27.671 33.3698 27.9317 33.5378 28.2249 33.6091ZM26.2944 29.9438L25.8692 30.1261C25.4383 30.3196 24.9713 30.4197 24.4989 30.4197C24.0266 30.4197 23.5596 30.3196 23.1287 30.1261L22.7034 29.9438C21.6313 29.4635 20.7207 28.6837 20.0813 27.6982C19.4418 26.7127 19.1007 25.5634 19.0989 24.3886V19.1978C21.4007 18.7051 23.4527 18.1853 25.4507 16.5451C25.5663 17.4074 25.4832 18.2848 25.2077 19.1101C25.1742 19.2116 25.1653 19.3196 25.1817 19.4252C25.1982 19.5308 25.2395 19.631 25.3022 19.7176C25.3636 19.8055 25.4454 19.8772 25.5407 19.9266C25.6359 19.976 25.7417 20.0015 25.8489 20.0011C26.7454 19.9999 27.6048 19.6431 28.2384 19.0088C28.3901 18.859 28.5258 18.6939 28.6434 18.5161L28.6637 18.5363C29.0056 18.8761 29.432 19.1184 29.8989 19.2383V24.3886C29.8972 25.5634 29.556 26.7127 28.9166 27.6982C28.2771 28.6837 27.3666 29.4635 26.2944 29.9438ZM10.9989 47.0011V41.8508C10.9966 40.1947 11.5491 38.5855 12.5683 37.28C13.5874 35.9746 15.0145 35.0482 16.6217 34.6486L18.0729 34.2841L15.8252 37.8683C15.7642 37.967 15.7297 38.0797 15.725 38.1955C15.7203 38.3114 15.7455 38.4265 15.7982 38.5298L20.0304 47.0011H10.9989ZM21.5424 47.0011L17.1752 38.2666L19.4297 34.6621L21.6639 47.0011H21.5424ZM25.9569 47.0011H23.0409L21.8462 40.4198C23.5503 41.0948 25.4476 41.0948 27.1517 40.4198L25.9569 47.0011ZM27.3339 47.0011L29.5682 34.6621L31.8227 38.2666L27.4554 47.0011H27.3339ZM37.9989 47.0011H28.9674L33.1997 38.5298C33.2524 38.4265 33.2776 38.3114 33.2729 38.1955C33.2681 38.0797 33.2336 37.967 33.1727 37.8683L30.9249 34.2841L32.3762 34.6486C33.9834 35.0482 35.4105 35.9746 36.4296 37.28C37.4487 38.5855 38.0012 40.1947 37.9989 41.8508V47.0011Z"
            fill="#DC688B"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M24.5 46.5005C36.6503 46.5005 46.5 36.6508 46.5 24.5005C46.5 12.3502 36.6503 2.50049 24.5 2.50049C12.3497 2.50049 2.5 12.3502 2.5 24.5005C2.5 36.6508 12.3497 46.5005 24.5 46.5005ZM24.5 48.5005C37.7549 48.5005 48.5 37.7553 48.5 24.5005C48.5 11.2457 37.7549 0.500488 24.5 0.500488C11.2452 0.500488 0.5 11.2457 0.5 24.5005C0.5 37.7553 11.2452 48.5005 24.5 48.5005Z"
            fill="#DA72B0"
          />
        </g>
        <defs>
          <clipPath id="clip0_2601_22257">
            <path
              d="M0.5 24.5005C0.5 11.2457 11.2452 0.500488 24.5 0.500488C37.7548 0.500488 48.5 11.2457 48.5 24.5005C48.5 37.7553 37.7548 48.5005 24.5 48.5005C11.2452 48.5005 0.5 37.7553 0.5 24.5005Z"
              fill="white"
            />
          </clipPath>
        </defs>
      </svg>
    </SvgIcon>
  );
};

export default IconFemale;
