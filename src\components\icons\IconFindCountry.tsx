import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconFindCountry = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-find-country ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_2685_33500"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_2685_33500)">
          <path
            d="M12.0016 21.5004C10.6877 21.5004 9.45268 21.2511 8.29655 20.7524C7.1404 20.2538 6.13472 19.577 5.2795 18.7222C4.42427 17.8673 3.74721 16.8621 3.24833 15.7065C2.74944 14.5508 2.5 13.3161 2.5 12.0021C2.5 10.6882 2.74933 9.45317 3.248 8.29704C3.74667 7.14089 4.42342 6.13521 5.27825 5.27999C6.1331 4.42476 7.13834 3.7477 8.29398 3.24881C9.44959 2.74993 10.6844 2.50049 11.9983 2.50049C13.3122 2.50049 14.5473 2.74982 15.7034 3.24849C16.8596 3.74716 17.8652 4.42391 18.7205 5.27874C19.5757 6.13359 20.2527 7.13883 20.7516 8.29446C21.2505 9.45008 21.5 10.6849 21.5 11.9988C21.5 13.3127 21.2506 14.5478 20.752 15.7039C20.2533 16.86 19.5765 17.8657 18.7217 18.7209C17.8669 19.5762 16.8616 20.2532 15.706 20.7521C14.5504 21.251 13.3156 21.5004 12.0016 21.5004ZM11 19.9505V18.0005C10.45 18.0005 9.97914 17.8046 9.58748 17.413C9.19581 17.0213 8.99998 16.5505 8.99998 16.0005V15.0005L4.19998 10.2005C4.14998 10.5005 4.10414 10.8005 4.06248 11.1005C4.02081 11.4005 3.99998 11.7005 3.99998 12.0005C3.99998 14.0171 4.66248 15.7838 5.98748 17.3005C7.31248 18.8171 8.98331 19.7005 11 19.9505ZM17.9 17.4005C18.2333 17.0338 18.5333 16.638 18.8 16.213C19.0666 15.788 19.2875 15.3463 19.4625 14.888C19.6375 14.4296 19.7708 13.9588 19.8625 13.4755C19.9541 12.9921 20 12.5005 20 12.0005C20 10.3623 19.5474 8.86621 18.6423 7.51221C17.7372 6.15823 16.5231 5.18124 15 4.58124V5.00046C15 5.55046 14.8041 6.0213 14.4125 6.41296C14.0208 6.80463 13.55 7.00046 13 7.00046H11V9.00046C11 9.2838 10.9041 9.5213 10.7125 9.71296C10.5208 9.90463 10.2833 10.0005 9.99998 10.0005H7.99998V12.0005H14C14.2833 12.0005 14.5208 12.0963 14.7125 12.288C14.9041 12.4796 15 12.7171 15 13.0005V16.0005H16C16.4333 16.0005 16.825 16.1296 17.175 16.388C17.525 16.6463 17.7666 16.9838 17.9 17.4005Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconFindCountry;
