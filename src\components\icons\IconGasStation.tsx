import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconGasStation = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-gas-station ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_4944_2980562"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_4944_2980562)">
          <path
            d="M3.82227 21.2549V4.71424C3.82227 4.16427 4.0128 3.69876 4.39386 3.31769C4.77493 2.93663 5.24045 2.74609 5.79041 2.74609H11.6529C12.2029 2.74609 12.6684 2.93663 13.0494 3.31769C13.4305 3.69876 13.621 4.16427 13.621 4.71424V11.7911H14.6679C15.2091 11.7911 15.6725 11.9838 16.0579 12.3692C16.4433 12.7547 16.636 13.218 16.636 13.7592V18.7005C16.636 19.0509 16.7578 19.3479 17.0014 19.5914C17.245 19.835 17.5419 19.9568 17.8923 19.9568C18.2426 19.9568 18.5396 19.835 18.7832 19.5914C19.0268 19.3479 19.1486 19.0509 19.1486 18.7005V11.0709C18.9853 11.1686 18.8129 11.2363 18.6314 11.274C18.45 11.3116 18.2594 11.3305 18.0598 11.3305C17.3915 11.3305 16.8266 11.0998 16.3651 10.6383C15.9037 10.1768 15.6729 9.61195 15.6729 8.94363C15.6729 8.40484 15.8265 7.92503 16.1336 7.50418C16.4406 7.08333 16.8468 6.80102 17.3521 6.65727L14.8982 4.20334L15.8111 3.29047L19.6929 7.10532C19.9372 7.34958 20.1239 7.63258 20.253 7.9543C20.3821 8.27603 20.4467 8.60581 20.4467 8.94363V18.7005C20.4467 19.4158 20.1999 20.0203 19.7065 20.5141C19.2131 21.008 18.609 21.2549 17.8944 21.2549C17.1797 21.2549 16.575 21.008 16.0802 20.5141C15.5853 20.0203 15.3379 19.4158 15.3379 18.7005V13.4243C15.3379 13.3265 15.3065 13.2463 15.2437 13.1834C15.1809 13.1206 15.1006 13.0892 15.0029 13.0892H13.621V21.2549H3.82227ZM5.45537 10.0952H11.9879V4.71424C11.9879 4.63048 11.953 4.5537 11.8832 4.48389C11.8134 4.4141 11.7366 4.3792 11.6529 4.3792H5.79041C5.70665 4.3792 5.62987 4.4141 5.56006 4.48389C5.49027 4.5537 5.45537 4.63048 5.45537 4.71424V10.0952ZM18.0598 10.0324C18.3683 10.0324 18.6269 9.92805 18.8356 9.71937C19.0442 9.51069 19.1486 9.25211 19.1486 8.94363C19.1486 8.63515 19.0442 8.37657 18.8356 8.16789C18.6269 7.95921 18.3683 7.85487 18.0598 7.85487C17.7513 7.85487 17.4928 7.95921 17.2841 8.16789C17.0754 8.37657 16.9711 8.63515 16.9711 8.94363C16.9711 9.25211 17.0754 9.51069 17.2841 9.71937C17.4928 9.92805 17.7513 10.0324 18.0598 10.0324ZM5.45537 19.6218H11.9879V11.7283H5.45537V19.6218Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconGasStation;
