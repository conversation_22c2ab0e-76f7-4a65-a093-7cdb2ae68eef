import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconGlobe = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-globe ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_2568_25950"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_2568_25950)">
          <path
            d="M12 21.5004C10.6974 21.5004 9.46825 21.2508 8.3125 20.7514C7.15673 20.252 6.14872 19.5722 5.28845 18.712C4.4282 17.8517 3.7484 16.8437 3.24905 15.6879C2.74968 14.5322 2.5 13.303 2.5 12.0005C2.5 10.6876 2.74968 9.45592 3.24905 8.30529C3.7484 7.15466 4.4282 6.14921 5.28845 5.28894C6.14872 4.42869 7.15673 3.74889 8.3125 3.24954C9.46825 2.75017 10.6974 2.50049 12 2.50049C13.3128 2.50049 14.5445 2.75017 15.6952 3.24954C16.8458 3.74889 17.8512 4.42869 18.7115 5.28894C19.5718 6.14921 20.2516 7.15466 20.7509 8.30529C21.2503 9.45592 21.5 10.6876 21.5 12.0005C21.5 13.303 21.2503 14.5322 20.7509 15.6879C20.2516 16.8437 19.5718 17.8517 18.7115 18.712C17.8512 19.5722 16.8458 20.252 15.6952 20.7514C14.5445 21.2508 13.3128 21.5004 12 21.5004ZM12 19.9793C12.5102 19.3024 12.9397 18.6197 13.2885 17.9312C13.6372 17.2427 13.9211 16.4902 14.1404 15.6735H9.85958C10.0916 16.5158 10.3788 17.2812 10.7211 17.9697C11.0634 18.6582 11.4897 19.328 12 19.9793ZM10.0635 19.7043C9.68014 19.1543 9.33591 18.529 9.03078 17.8283C8.72564 17.1277 8.48846 16.4094 8.31922 15.6735H4.92688C5.45509 16.712 6.16343 17.5844 7.0519 18.2909C7.94038 18.9973 8.94424 19.4684 10.0635 19.7043ZM13.9365 19.7043C15.0557 19.4684 16.0596 18.9973 16.9481 18.2909C17.8365 17.5844 18.5449 16.712 19.0731 15.6735H15.6807C15.4794 16.4158 15.2262 17.1373 14.9211 17.838C14.616 18.5386 14.2878 19.1607 13.9365 19.7043ZM4.29805 14.1736H8.01538C7.95256 13.8018 7.90705 13.4373 7.87885 13.0803C7.85065 12.7232 7.83655 12.3633 7.83655 12.0005C7.83655 11.6376 7.85065 11.2777 7.87885 10.9206C7.90705 10.5636 7.95256 10.1992 8.01538 9.82736H4.29805C4.2019 10.1671 4.12818 10.5203 4.0769 10.887C4.02562 11.2537 3.99998 11.6248 3.99998 12.0005C3.99998 12.3761 4.02562 12.7473 4.0769 13.1139C4.12818 13.4806 4.2019 13.8338 4.29805 14.1736ZM9.51535 14.1736H14.4846C14.5474 13.8018 14.5929 13.4405 14.6212 13.0899C14.6494 12.7393 14.6635 12.3761 14.6635 12.0005C14.6635 11.6248 14.6494 11.2617 14.6212 10.911C14.5929 10.5604 14.5474 10.1992 14.4846 9.82736H9.51535C9.45253 10.1992 9.40702 10.5604 9.3788 10.911C9.3506 11.2617 9.3365 11.6248 9.3365 12.0005C9.3365 12.3761 9.3506 12.7393 9.3788 13.0899C9.40702 13.4405 9.45253 13.8018 9.51535 14.1736ZM15.9846 14.1736H19.7019C19.7981 13.8338 19.8718 13.4806 19.9231 13.1139C19.9743 12.7473 20 12.3761 20 12.0005C20 11.6248 19.9743 11.2537 19.9231 10.887C19.8718 10.5203 19.7981 10.1671 19.7019 9.82736H15.9846C16.0474 10.1992 16.0929 10.5636 16.1211 10.9206C16.1493 11.2777 16.1634 11.6376 16.1634 12.0005C16.1634 12.3633 16.1493 12.7232 16.1211 13.0803C16.0929 13.4373 16.0474 13.8018 15.9846 14.1736ZM15.6807 8.32741H19.0731C18.5384 7.27611 17.8349 6.40366 16.9625 5.71006C16.09 5.01648 15.0814 4.54212 13.9365 4.28699C14.3198 4.86904 14.6608 5.50558 14.9596 6.19661C15.2583 6.88765 15.4987 7.59791 15.6807 8.32741ZM9.85958 8.32741H14.1404C13.9083 7.49151 13.6163 6.72131 13.2644 6.01681C12.9125 5.31233 12.491 4.64726 12 4.02161C11.5089 4.64726 11.0875 5.31233 10.7356 6.01681C10.3836 6.72131 10.0916 7.49151 9.85958 8.32741ZM4.92688 8.32741H8.31922C8.50128 7.59791 8.74167 6.88765 9.0404 6.19661C9.33912 5.50558 9.68014 4.86904 10.0635 4.28699C8.91219 4.54212 7.90193 5.01809 7.03268 5.71489C6.16344 6.41169 5.46151 7.28253 4.92688 8.32741Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconGlobe;
