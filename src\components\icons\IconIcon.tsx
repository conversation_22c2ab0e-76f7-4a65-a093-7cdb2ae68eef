import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconIcon = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-icon ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="32"
        height="32"
        viewBox="0 0 32 32"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5.6696 21.998L9.77919 27.2035C10.0355 27.5282 9.98011 27.9992 9.65542 28.2555C9.33074 28.5118 8.85974 28.4564 8.60341 28.1318L3.6425 21.8479C3.21163 21.3022 3.60036 20.5 4.2957 20.5H27.5998C28.2951 20.5 28.6838 21.3022 28.253 21.8479L23.2921 28.1318C23.0357 28.4564 22.5647 28.5118 22.2401 28.2555C21.9154 27.9992 21.86 27.5282 22.1163 27.2035L26.2259 21.998H5.6696Z"
          fill={color}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.7621 6.56729C12.0858 5.55463 13.3628 3.58447 15.9793 3.58447C18.5863 3.58447 19.9114 5.54219 20.2631 6.54697C20.296 6.64089 20.3096 6.73386 20.3096 6.82104V9.91621C20.3096 10.1052 20.2453 10.2885 20.1272 10.4361L18.5374 12.4234V12.7221L23.9505 15.8056C24.2104 15.9536 24.3708 16.2296 24.3708 16.5287V18.954C24.3708 19.3677 24.0355 19.703 23.6218 19.703C23.2081 19.703 22.8728 19.3677 22.8728 18.954V16.9157L17.4597 13.8323C17.1998 13.6842 17.0394 13.4082 17.0394 13.1091V12.1898C17.0394 12.0009 17.1037 11.8175 17.2217 11.6699L18.8115 9.68266V6.94337C18.5323 6.26661 17.6375 5.0825 15.9793 5.0825C14.3233 5.0825 13.4734 6.26495 13.2208 6.93243V9.70472L14.6526 11.7026C14.7539 11.8439 14.8084 12.0135 14.8084 12.1874V13.1094C14.8084 13.4068 14.6497 13.6817 14.3921 13.8303L9.04881 16.9129V18.954C9.04881 19.3677 8.71346 19.703 8.2998 19.703C7.88613 19.703 7.55078 19.3677 7.55078 18.954V16.5284C7.55078 16.231 7.7095 15.9562 7.96713 15.8075L13.3104 12.7249V12.4013L11.8786 10.4035C11.7773 10.2621 11.7228 10.0926 11.7228 9.91866V6.81989C11.7228 6.73986 11.7343 6.65441 11.7621 6.56729ZM15.9793 14.8452C16.393 14.8452 16.7283 15.1806 16.7283 15.5942V18.954C16.7283 19.3677 16.393 19.703 15.9793 19.703C15.5656 19.703 15.2303 19.3677 15.2303 18.954V15.5942C15.2303 15.1806 15.5656 14.8452 15.9793 14.8452Z"
          fill={color}
        />
      </svg>
    </SvgIcon>
  );
};

export default IconIcon;
