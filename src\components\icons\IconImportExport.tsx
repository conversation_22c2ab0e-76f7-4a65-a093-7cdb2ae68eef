import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconImportExport = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-import-export ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_11555_181186"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="24"
        >
          <rect width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_11555_181186)">
          <path
            d="M5.42293 20.1921L3.58643 9.19212H20.4134L18.5769 20.1921H5.42293ZM6.67493 18.6921H17.3249L18.5999 10.6921H5.39993L6.67493 18.6921ZM9.99993 14.4421H13.9999C14.2124 14.4421 14.3905 14.3702 14.5342 14.2264C14.678 14.0825 14.7499 13.9044 14.7499 13.6919C14.7499 13.4792 14.678 13.3011 14.5342 13.1576C14.3905 13.014 14.2124 12.9421 13.9999 12.9421H9.99993C9.78743 12.9421 9.60934 13.014 9.46568 13.1579C9.32184 13.3017 9.24993 13.4799 9.24993 13.6924C9.24993 13.905 9.32184 14.0832 9.46568 14.2269C9.60934 14.3704 9.78743 14.4421 9.99993 14.4421ZM5.99993 7.99987C5.78743 7.99987 5.60934 7.92795 5.46568 7.78412C5.32184 7.64028 5.24993 7.46212 5.24993 7.24962C5.24993 7.03695 5.32184 6.85887 5.46568 6.71537C5.60934 6.5717 5.78743 6.49987 5.99993 6.49987H17.9999C18.2124 6.49987 18.3905 6.57178 18.5342 6.71562C18.678 6.85945 18.7499 7.03762 18.7499 7.25012C18.7499 7.46278 18.678 7.64087 18.5342 7.78437C18.3905 7.92803 18.2124 7.99987 17.9999 7.99987H5.99993ZM7.99993 5.30762C7.78743 5.30762 7.60934 5.2357 7.46568 5.09187C7.32184 4.94803 7.24993 4.76987 7.24993 4.55737C7.24993 4.3447 7.32184 4.16662 7.46568 4.02312C7.60934 3.87945 7.78743 3.80762 7.99993 3.80762H15.9999C16.2124 3.80762 16.3905 3.87953 16.5342 4.02337C16.678 4.1672 16.7499 4.34537 16.7499 4.55787C16.7499 4.77053 16.678 4.94862 16.5342 5.09212C16.3905 5.23578 16.2124 5.30762 15.9999 5.30762H7.99993Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconImportExport;
