import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconInvite = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-invite ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_3170_54712"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_3170_54712)">
          <path
            d="M1.73096 22.2696L3.48863 16.262C3.17197 15.5851 2.92806 14.8914 2.75691 14.1811C2.58574 13.4707 2.50016 12.7439 2.50016 12.0005C2.50016 10.6863 2.74949 9.45131 3.24816 8.29549C3.74682 7.13966 4.42357 6.13424 5.27841 5.27924C6.13326 4.42424 7.1385 3.74736 8.29413 3.24861C9.44975 2.74986 10.6845 2.50049 11.9985 2.50049C13.3124 2.50049 14.5474 2.74982 15.7036 3.24849C16.8597 3.74716 17.8654 4.42391 18.7206 5.27874C19.5758 6.13359 20.2529 7.13883 20.7518 8.29446C21.2507 9.45008 21.5001 10.6849 21.5001 11.9988C21.5001 13.3127 21.2507 14.5478 20.752 15.7039C20.2532 16.86 19.5764 17.8657 18.7214 18.7209C17.8664 19.5762 16.8609 20.2532 15.7051 20.7521C14.5493 21.251 13.3143 21.5004 12.0001 21.5004C11.2567 21.5004 10.5299 21.4149 9.81951 21.2437C9.10916 21.0725 8.41552 20.8286 7.73861 20.512L1.73096 22.2696ZM3.95013 20.0505L7.15013 19.1005C7.39628 19.0338 7.63378 19.0056 7.86263 19.0158C8.09148 19.0261 8.32065 19.0876 8.55013 19.2005C9.08347 19.4671 9.6418 19.6671 10.2251 19.8005C10.8085 19.9338 11.4001 20.0005 12.0001 20.0005C14.2335 20.0005 16.1251 19.2255 17.6751 17.6755C19.2251 16.1255 20.0001 14.2338 20.0001 12.0005C20.0001 9.76713 19.2251 7.87546 17.6751 6.32546C16.1251 4.77546 14.2335 4.00046 12.0001 4.00046C9.7668 4.00046 7.87513 4.77546 6.32513 6.32546C4.77513 7.87546 4.00013 9.76713 4.00013 12.0005C4.00013 12.6005 4.0668 13.1921 4.20013 13.7755C4.33347 14.3588 4.53347 14.9171 4.80013 15.4505C4.9168 15.6671 4.9809 15.8963 4.99243 16.138C5.00398 16.3796 4.97322 16.6171 4.90013 16.8505L3.95013 20.0505ZM11.2502 15.7504H12.7501V12.7504H15.7501V11.2505H12.7501V8.25049H11.2502V11.2505H8.25016V12.7504H11.2502V15.7504Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconInvite;
