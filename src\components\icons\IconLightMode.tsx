import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconLightMode = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-light-mode ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_2473_22045"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_2473_22045)">
          <path
            d="M12 15.0005C12.8333 15.0005 13.5417 14.7088 14.125 14.1255C14.7083 13.5421 15 12.8338 15 12.0005C15 11.1671 14.7083 10.4588 14.125 9.87546C13.5417 9.29213 12.8333 9.00046 12 9.00046C11.1667 9.00046 10.4583 9.29213 9.875 9.87546C9.29167 10.4588 9 11.1671 9 12.0005C9 12.8338 9.29167 13.5421 9.875 14.1255C10.4583 14.7088 11.1667 15.0005 12 15.0005ZM12 16.5004C10.7513 16.5004 9.68913 16.0626 8.81348 15.187C7.93784 14.3113 7.50003 13.2492 7.50003 12.0005C7.50003 10.7518 7.93784 9.68959 8.81348 8.81394C9.68913 7.93831 10.7513 7.50049 12 7.50049C13.2487 7.50049 14.3109 7.93831 15.1865 8.81394C16.0622 9.68959 16.5 10.7518 16.5 12.0005C16.5 13.2492 16.0622 14.3113 15.1865 15.187C14.3109 16.0626 13.2487 16.5004 12 16.5004ZM2 12.7504C1.7875 12.7504 1.60938 12.6785 1.46565 12.5347C1.32188 12.3909 1.25 12.2127 1.25 12.0001C1.25 11.7875 1.32188 11.6094 1.46563 11.4659C1.60936 11.3223 1.78747 11.2505 1.99998 11.2505H4.25C4.46248 11.2505 4.6406 11.3224 4.78435 11.4662C4.92812 11.61 5 11.7882 5 12.0008C5 12.2134 4.92812 12.3915 4.78437 12.5351C4.64062 12.6786 4.46251 12.7504 4.25003 12.7504H2ZM19.75 12.7504C19.5375 12.7504 19.3594 12.6785 19.2156 12.5347C19.0719 12.3909 19 12.2127 19 12.0001C19 11.7875 19.0719 11.6094 19.2156 11.4659C19.3594 11.3223 19.5375 11.2505 19.75 11.2505H22C22.2125 11.2505 22.3906 11.3224 22.5344 11.4662C22.6781 11.61 22.75 11.7882 22.75 12.0008C22.75 12.2134 22.6781 12.3915 22.5344 12.5351C22.3906 12.6786 22.2125 12.7504 22 12.7504H19.75ZM11.9997 5.00046C11.7871 5.00046 11.609 4.92859 11.4654 4.78484C11.3218 4.64109 11.25 4.46297 11.25 4.25049V2.00046C11.25 1.78796 11.3219 1.60984 11.4657 1.46609C11.6095 1.32236 11.7877 1.25049 12.0003 1.25049C12.2129 1.25049 12.391 1.32236 12.5346 1.46609C12.6782 1.60984 12.75 1.78796 12.75 2.00046V4.25049C12.75 4.46297 12.6781 4.64109 12.5343 4.78484C12.3905 4.92859 12.2123 5.00046 11.9997 5.00046ZM11.9997 22.7505C11.7871 22.7505 11.609 22.6786 11.4654 22.5348C11.3218 22.3911 11.25 22.213 11.25 22.0005V19.7504C11.25 19.538 11.3219 19.3598 11.4657 19.2161C11.6095 19.0723 11.7877 19.0005 12.0003 19.0005C12.2129 19.0005 12.391 19.0723 12.5346 19.2161C12.6782 19.3598 12.75 19.538 12.75 19.7504V22.0005C12.75 22.213 12.6781 22.3911 12.5343 22.5348C12.3905 22.6786 12.2123 22.7505 11.9997 22.7505ZM6.0058 7.04081L4.7481 5.81774C4.59938 5.67927 4.52758 5.50524 4.5327 5.29564C4.53783 5.08602 4.61061 4.90365 4.75102 4.74854C4.90419 4.59341 5.08398 4.51584 5.29038 4.51584C5.49679 4.51584 5.67243 4.59341 5.8173 4.74854L7.05 5.99664C7.19487 6.15177 7.2673 6.32741 7.2673 6.52356C7.2673 6.71971 7.19647 6.89535 7.0548 7.05046C6.91315 7.20558 6.74168 7.27897 6.5404 7.27064C6.33912 7.26231 6.16092 7.18568 6.0058 7.04081ZM18.1827 19.2523L16.95 18.0043C16.8051 17.8492 16.7327 17.671 16.7327 17.4697C16.7327 17.2684 16.8051 17.0953 16.95 16.9505C17.0852 16.7953 17.2543 16.722 17.4572 16.7303C17.6601 16.7386 17.8391 16.8152 17.9942 16.9601L19.2519 18.1832C19.4006 18.3216 19.4724 18.4957 19.4673 18.7053C19.4622 18.9149 19.3894 19.0972 19.249 19.2524C19.0958 19.4075 18.916 19.4851 18.7096 19.4851C18.5032 19.4851 18.3276 19.4075 18.1827 19.2523ZM16.95 7.05526C16.7949 6.91361 16.7215 6.74215 16.7298 6.54086C16.7382 6.33958 16.8148 6.16138 16.9596 6.00626L18.1827 4.74856C18.3212 4.59985 18.4952 4.52805 18.7048 4.53316C18.9144 4.5383 19.0968 4.61107 19.2519 4.75149C19.407 4.90466 19.4846 5.08444 19.4846 5.29084C19.4846 5.49726 19.407 5.6729 19.2519 5.81776L18.0038 7.05046C17.8487 7.19533 17.673 7.26776 17.4769 7.26776C17.2808 7.26776 17.1051 7.19693 16.95 7.05526ZM4.7481 19.2545C4.59297 19.0979 4.5154 18.9165 4.5154 18.7101C4.5154 18.5037 4.59297 18.328 4.7481 18.1832L5.99618 16.9505C6.15131 16.8056 6.32951 16.7332 6.53077 16.7332C6.73206 16.7332 6.90513 16.8056 7.05 16.9505C7.19872 17.0857 7.26891 17.2548 7.26058 17.4577C7.25224 17.6605 7.17884 17.8395 7.04037 17.9947L5.8173 19.2524C5.67243 19.4075 5.49679 19.4825 5.29038 19.4774C5.08398 19.4722 4.90322 19.3979 4.7481 19.2545Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconLightMode;
