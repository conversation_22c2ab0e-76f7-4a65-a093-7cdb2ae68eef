import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconLocalLibrary = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-local-library ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_13161_343444"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="24"
        >
          <rect width="24" height="24" fill={color} />
        </mask>
        <g mask="url(#mask0_13161_343444)">
          <path
            d="M12 21.8085C10.8384 20.7842 9.53076 19.9896 8.07693 19.4249C6.62308 18.8601 5.09743 18.5521 3.5 18.5008V8.32782C5.10642 8.37268 6.64135 8.7073 8.1048 9.33167C9.56825 9.95603 10.8666 10.7919 12 11.8393C13.1333 10.7919 14.4317 9.95603 15.8952 9.33167C17.3586 8.7073 18.8935 8.37268 20.5 8.32782V18.5008C18.8923 18.5521 17.3641 18.8601 15.9154 19.4249C14.4666 19.9896 13.1615 20.7842 12 21.8085ZM12 19.8816C13.05 19.1047 14.1666 18.4815 15.35 18.0121C16.5333 17.5427 17.75 17.2325 19 17.0816V9.98932C17.7384 10.206 16.517 10.6355 15.3356 11.2778C14.1541 11.9201 13.0423 12.7624 12 13.8047C10.9577 12.7624 9.84582 11.9201 8.6644 11.2778C7.483 10.6355 6.26153 10.206 4.99997 9.98932V17.0816C6.24998 17.2325 7.46664 17.5427 8.64998 18.0121C9.83331 18.4815 10.95 19.1047 12 19.8816ZM12 8.78932C11.0058 8.78932 10.1546 8.43531 9.44663 7.72729C8.73863 7.01929 8.38462 6.16819 8.38462 5.17397C8.38462 4.17974 8.73863 3.32862 9.44663 2.62062C10.1546 1.9126 11.0058 1.55859 12 1.55859C12.9942 1.55859 13.8453 1.9126 14.5533 2.62062C15.2613 3.32862 15.6153 4.17974 15.6153 5.17397C15.6153 6.16819 15.2613 7.01929 14.5533 7.72729C13.8453 8.43531 12.9942 8.78932 12 8.78932ZM12.0003 7.28934C12.5821 7.28934 13.0801 7.08218 13.4942 6.66784C13.9083 6.25353 14.1154 5.75545 14.1154 5.17362C14.1154 4.59178 13.9082 4.09382 13.4939 3.67972C13.0795 3.26562 12.5815 3.05857 11.9996 3.05857C11.4178 3.05857 10.9199 3.26574 10.5058 3.68007C10.0916 4.0944 9.88458 4.59248 9.88458 5.17429C9.88458 5.75613 10.0917 6.25409 10.5061 6.66819C10.9204 7.08229 11.4185 7.28934 12.0003 7.28934Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconLocalLibrary;
