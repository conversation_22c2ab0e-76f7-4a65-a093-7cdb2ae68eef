import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconLocationPerson = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-location-person ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_8183_227674"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_8183_227674)">
          <path
            d="M12.0002 14.7504C12.8438 14.7504 13.6101 14.5501 14.2992 14.1495C14.9883 13.7489 15.5431 13.2184 15.9636 12.5582C15.4124 12.1325 14.8005 11.8082 14.1281 11.5851C13.4556 11.362 12.7463 11.2505 12.0002 11.2505C11.254 11.2505 10.5448 11.362 9.87232 11.5851C9.19989 11.8082 8.58804 12.1325 8.03677 12.5582C8.45727 13.2184 9.01207 13.7489 9.70117 14.1495C10.3903 14.5501 11.1566 14.7504 12.0002 14.7504ZM12.0002 9.75044C12.4861 9.75044 12.8992 9.58025 13.2396 9.23986C13.58 8.8995 13.7502 8.48636 13.7502 8.00046C13.7502 7.51456 13.58 7.10143 13.2396 6.76106C12.8992 6.42068 12.4861 6.25049 12.0002 6.25049C11.5143 6.25049 11.1012 6.42068 10.7608 6.76106C10.4204 7.10143 10.2502 7.51456 10.2502 8.00046C10.2502 8.48636 10.4204 8.8995 10.7608 9.23986C11.1012 9.58025 11.5143 9.75044 12.0002 9.75044ZM12.0002 19.5139C13.9566 17.7627 15.4537 16.0829 16.4915 14.4745C17.5294 12.8662 18.0483 11.4575 18.0483 10.2485C18.0483 8.42547 17.4691 6.92676 16.3108 5.75239C15.1524 4.57802 13.7156 3.99084 12.0002 3.99084C10.2848 3.99084 8.84796 4.57802 7.68962 5.75239C6.53129 6.92676 5.95212 8.42547 5.95212 10.2485C5.95212 11.4575 6.47103 12.8662 7.50885 14.4745C8.54668 16.0829 10.0438 17.7627 12.0002 19.5139ZM12.0002 21.51C9.48355 19.3293 7.59637 17.2998 6.33867 15.4216C5.08099 13.5434 4.45215 11.8191 4.45215 10.2485C4.45215 7.94087 5.19862 6.07261 6.69157 4.64376C8.18451 3.21491 9.95405 2.50049 12.0002 2.50049C14.0463 2.50049 15.8159 3.21491 17.3088 4.64376C18.8018 6.07261 19.5482 7.94087 19.5482 10.2485C19.5482 11.8191 18.9194 13.5434 17.6617 15.4216C16.404 17.2998 14.5168 19.3293 12.0002 21.51Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconLocationPerson;
