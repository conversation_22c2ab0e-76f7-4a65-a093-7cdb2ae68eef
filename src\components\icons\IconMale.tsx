import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconMale = ({
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-male ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="49"
        height="49"
        viewBox="0 0 49 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_2601_22242)">
          <g clipPath="url(#clip1_2601_22242)">
            <path
              d="M15.5005 37.0997C12.6205 37.5797 10.3005 40.4997 9.50049 41.8997C11.7005 43.6997 17.7805 47.2996 24.5005 47.2996C32.9005 47.2996 38.9005 42.4996 38.9005 41.2996C38.9005 40.0996 35.3005 38.2996 33.5005 37.0997C32.0605 36.1398 28.9005 36.2998 27.5005 36.4997V34.6996C27.3005 34.2997 27.2605 33.3797 28.7005 32.8996C30.1405 32.4195 31.3005 29.0996 31.7005 27.4997L32.9005 26.2996C33.1005 25.8996 33.5005 24.7396 33.5005 23.2996C33.5005 21.8596 32.3005 21.4996 31.7005 21.4996L32.9005 20.2996C33.3005 19.0996 33.9805 16.2196 33.5005 14.2996C33.0205 12.3796 29.3005 12.2996 27.5005 12.4996C26.7005 11.6996 24.6205 9.97973 22.7005 9.49973C20.3005 8.89973 16.7005 10.6997 14.9005 13.6997C13.4605 16.0997 14.3005 19.4997 14.9005 20.8997C15.3005 21.0997 15.9805 21.7397 15.5005 22.6997C14.9005 23.8997 15.5005 26.2997 16.1005 27.4997C16.5805 28.4597 19.5005 31.8997 20.9005 33.4997V36.4997C20.3005 36.4997 18.3805 36.6197 15.5005 37.0997Z"
              fill="#FEFEFE"
            />
            <path
              d="M20.3008 20.8995C18.8608 20.8995 16.5008 21.6995 15.5008 22.0995C15.1008 20.2995 14.3008 16.3395 14.3008 14.8995C14.3008 13.0995 17.9008 9.49951 22.1008 9.49951C25.4608 9.49951 27.1008 11.4995 27.5008 12.4995C27.5008 12.2995 27.7408 11.8995 28.7008 11.8995C29.9008 11.8995 31.1008 11.8995 33.5008 14.8995C35.4208 17.2995 33.5008 19.8995 32.3008 20.8995C32.1008 20.8995 31.5808 20.6595 31.1008 19.6995C30.6208 18.7395 28.5008 17.2995 27.5008 16.6995C25.7008 18.0995 21.7408 20.8995 20.3008 20.8995Z"
              fill="#A5B3DD"
            />
            <path
              d="M23.3006 39.4995L22.1006 47.2996H26.9006L25.7006 39.4995H23.3006Z"
              fill="#566BD0"
            />
            <path
              d="M30.1121 35.7874H28.1934V33.7407C29.3053 33.0354 30.2603 32.109 30.999 31.019C31.7378 29.9291 32.2446 28.6989 32.4879 27.4048C33.7995 27.0253 34.589 25.0773 34.589 23.6358C34.6905 22.5528 34.0212 21.5461 32.9835 21.2203C34.7061 19.4315 35.0855 16.739 33.9247 14.5437C32.7639 12.3484 30.3248 11.1463 27.8767 11.5629C24.8766 8.32483 19.8688 7.9961 16.4709 10.8134C13.0729 13.6312 12.469 18.6134 15.0958 22.1609C14.7697 22.4038 14.5097 22.7247 14.3398 23.0941C14.1699 23.4635 14.0954 23.8697 14.1231 24.2754C14.1231 25.7725 14.9722 27.8192 16.3763 28.0834C16.9924 30.425 18.4725 32.4463 20.5187 33.7407V35.7874H18.6C17.1717 35.7857 15.757 36.0657 14.4371 36.6115C13.1171 37.1573 11.9178 37.9581 10.9078 38.968C9.89793 39.978 9.09719 41.1773 8.55147 42.4972C8.00574 43.8171 7.72576 45.2317 7.72755 46.6599C7.72755 47.0131 8.01359 47.2995 8.36711 47.2995H40.345C40.5146 47.2995 40.6773 47.2321 40.7972 47.1121C40.9172 46.9922 40.9845 46.8295 40.9845 46.6599C40.9777 40.6581 36.1138 35.7943 30.1121 35.7874ZM21.7978 36.8536V34.4343C21.8553 34.459 21.9159 34.4746 21.9743 34.498C22.2232 34.6015 22.4765 34.6943 22.7334 34.7762C22.8299 34.8068 22.9252 34.8403 23.0238 34.8659C23.3644 34.965 23.7099 35.0457 24.0591 35.1076L24.2511 35.1388C24.3204 35.151 24.3914 35.151 24.4607 35.1388L24.6527 35.1076C25.0022 35.047 25.3478 34.9677 25.6888 34.8697C25.7867 34.8415 25.8807 34.8103 25.9796 34.7793C26.2379 34.6978 26.4923 34.6047 26.7422 34.5005C26.7984 34.4777 26.8584 34.4624 26.9149 34.4369V36.8517L25.3095 38.9852H23.3967L21.7978 36.8536ZM32.6703 25.7022V22.4551C33.1082 22.6216 33.3099 23.0041 33.3099 23.6358C33.2992 24.3717 33.0772 25.089 32.6703 25.7022ZM21.7978 10.2051C22.8169 10.2047 23.8239 10.4257 24.7492 10.8529C25.6745 11.28 26.4959 11.9031 27.1566 12.679C27.2327 12.7682 27.3319 12.8348 27.4434 12.8714C27.5548 12.9081 27.6742 12.9134 27.7884 12.8867C29.6848 12.4308 31.6585 13.2549 32.6672 14.9247C33.6758 16.5942 33.4872 18.7245 32.2013 20.1914C32.1841 20.1617 32.159 20.1273 32.1413 20.1004C32.0528 19.9536 31.9583 19.8105 31.8581 19.6714C31.8258 19.6265 31.794 19.5824 31.7619 19.5362C31.6145 19.3413 31.4595 19.1523 31.2972 18.9697C31.2619 18.9301 31.2223 18.8891 31.1863 18.8495C31.0322 18.6813 30.8739 18.5171 30.7114 18.357C30.6548 18.3014 30.598 18.2459 30.5393 18.1893C30.3154 17.9735 30.0857 17.7639 29.8504 17.5607C29.231 17.0285 28.583 16.5306 27.9092 16.0692C27.7987 15.9953 27.6678 15.9576 27.5349 15.9615C27.4019 15.9654 27.2735 16.0107 27.1675 16.0911L23.6927 18.7274C22.2641 19.8139 20.5226 20.4095 18.7278 20.4253H18.5775C18.4489 20.4269 18.3209 20.4381 18.1937 20.4587C18.1754 20.4587 18.1578 20.4662 18.1394 20.4699C18.0276 20.4899 17.9171 20.517 17.8087 20.5514L17.7768 20.5626C17.6607 20.6013 17.5475 20.6486 17.4384 20.7041H17.4328C16.9858 20.929 16.6158 21.2816 16.3697 21.7172C14.6365 19.6161 14.2717 16.7034 15.4332 14.2402C16.5948 11.7765 19.0741 10.2048 21.7978 10.2051ZM15.4022 24.2754C15.3707 24.0529 15.4064 23.8261 15.5047 23.6241C15.603 23.422 15.7595 23.2539 15.954 23.1415C15.9834 23.1702 16.0116 23.2002 16.0418 23.2283V25.6442C16.0418 25.893 16.0512 26.1407 16.0706 26.3874C15.6453 25.7642 15.4129 25.0297 15.4022 24.2754ZM17.498 27.3254C17.3793 26.7729 17.32 26.2093 17.3209 25.6442V22.9516C17.324 22.8342 17.3443 22.7183 17.3809 22.6069C17.3874 22.5856 17.3937 22.5641 17.4015 22.5429C17.4412 22.4307 17.4967 22.3247 17.5663 22.2281C17.5735 22.2187 17.5817 22.2103 17.5889 22.2012C17.6621 22.1062 17.7481 22.0219 17.8446 21.9508L17.8521 21.9442C17.9507 21.8754 18.0583 21.8205 18.1719 21.7811C18.19 21.7746 18.2078 21.7696 18.2265 21.764C18.3428 21.7269 18.4639 21.7068 18.586 21.7044H18.7309C20.8174 21.6853 22.8413 20.9896 24.4984 19.7217L27.5685 17.3939C27.9099 17.6406 28.4479 18.0441 29.0163 18.5322C29.177 18.6699 29.3348 18.8109 29.4897 18.9551C30.3145 19.6113 30.9565 20.4687 31.354 21.4449C31.3774 21.5336 31.39 21.6251 31.3912 21.7172V25.6441C31.3925 26.0122 31.3664 26.3798 31.3131 26.744C31.1435 28.0052 30.6875 29.2108 29.9798 30.2684C29.2722 31.326 28.3317 32.2075 27.2306 32.8454C26.3706 33.3476 25.4257 33.6876 24.4429 33.8485L24.356 33.8596L24.2714 33.8463C23.288 33.6799 22.3421 33.3397 21.478 32.8416C20.4635 32.2545 19.5844 31.4595 18.8985 30.5089C18.2126 29.5583 17.7353 28.4734 17.498 27.3254ZM11.8115 39.873C12.7021 38.9807 13.7604 38.2734 14.9255 37.7917C16.0905 37.3101 17.3393 37.0636 18.6 37.0666H20.5187C20.5187 37.2049 20.5634 37.3395 20.6465 37.4503L22.3977 39.7847L21.2573 46.6599L9.02761 46.0204C9.17632 43.7007 10.1661 41.5149 11.8115 39.873ZM22.559 46.6599L23.6116 40.2643H25.103L26.1405 46.6599H22.559ZM27.4434 46.6599L26.3156 39.7847L27.9683 37.578L28.0648 37.4504C28.148 37.3397 28.1932 37.205 28.1934 37.0666H30.1121C35.1595 37.0728 39.3413 40.9845 39.6842 46.0204L27.4434 46.6599Z"
              fill="#3B4CA3"
            />
          </g>
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M24.5 46.5005C36.6503 46.5005 46.5 36.6508 46.5 24.5005C46.5 12.3502 36.6503 2.50049 24.5 2.50049C12.3497 2.50049 2.5 12.3502 2.5 24.5005C2.5 36.6508 12.3497 46.5005 24.5 46.5005ZM24.5 48.5005C37.7549 48.5005 48.5 37.7553 48.5 24.5005C48.5 11.2457 37.7549 0.500488 24.5 0.500488C11.2452 0.500488 0.5 11.2457 0.5 24.5005C0.5 37.7553 11.2452 48.5005 24.5 48.5005Z"
            fill="#3B4CA3"
          />
        </g>
        <defs>
          <clipPath id="clip0_2601_22242">
            <path
              d="M0.5 24.5005C0.5 11.2457 11.2452 0.500488 24.5 0.500488C37.7548 0.500488 48.5 11.2457 48.5 24.5005C48.5 37.7553 37.7548 48.5005 24.5 48.5005C11.2452 48.5005 0.5 37.7553 0.5 24.5005Z"
              fill="white"
            />
          </clipPath>
          <clipPath id="clip1_2601_22242">
            <rect
              width="38.4"
              height="38.4"
              fill="white"
              transform="translate(7.7002 8.90039)"
            />
          </clipPath>
        </defs>
      </svg>
    </SvgIcon>
  );
};

export default IconMale;
