import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconMyLocation = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-my-location ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_4944_2979469"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_4944_2979469)">
          <path
            d="M11.2503 22.2007V20.4699C9.16694 20.2558 7.41309 19.4366 5.98872 18.0123C4.56437 16.5879 3.74514 14.834 3.53104 12.7507H1.80029V11.2508H3.53104C3.74514 9.16742 4.56437 7.41357 5.98872 5.98921C7.41309 4.56486 9.16694 3.74563 11.2503 3.53153V1.80078H12.7502V3.53153C14.8336 3.74563 16.5874 4.56486 18.0118 5.98921C19.4361 7.41357 20.2553 9.16742 20.4694 11.2508H22.2002V12.7507H20.4694C20.2553 14.834 19.4361 16.5879 18.0118 18.0123C16.5874 19.4366 14.8336 20.2558 12.7502 20.4699V22.2007H11.2503ZM12.0002 19.0007C13.9336 19.0007 15.5836 18.3174 16.9502 16.9507C18.3169 15.5841 19.0002 13.9341 19.0002 12.0007C19.0002 10.0674 18.3169 8.4174 16.9502 7.05073C15.5836 5.68406 13.9336 5.00073 12.0002 5.00073C10.0669 5.00073 8.41691 5.68406 7.05024 7.05073C5.68358 8.4174 5.00024 10.0674 5.00024 12.0007C5.00024 13.9341 5.68358 15.5841 7.05024 16.9507C8.41691 18.3174 10.0669 19.0007 12.0002 19.0007ZM12.0002 15.5007C11.0378 15.5007 10.2138 15.158 9.52839 14.4726C8.84298 13.7872 8.50027 12.9632 8.50027 12.0007C8.50027 11.0382 8.84298 10.2143 9.52839 9.52888C10.2138 8.84346 11.0378 8.50076 12.0002 8.50076C12.9627 8.50076 13.7867 8.84346 14.4721 9.52888C15.1575 10.2143 15.5002 11.0382 15.5002 12.0007C15.5002 12.9632 15.1575 13.7872 14.4721 14.4726C13.7867 15.158 12.9627 15.5007 12.0002 15.5007ZM12.0002 14.0007C12.5502 14.0007 13.0211 13.8049 13.4127 13.4132C13.8044 13.0216 14.0002 12.5507 14.0002 12.0007C14.0002 11.4507 13.8044 10.9799 13.4127 10.5882C13.0211 10.1966 12.5502 10.0007 12.0002 10.0007C11.4502 10.0007 10.9794 10.1966 10.5877 10.5882C10.1961 10.9799 10.0002 11.4507 10.0002 12.0007C10.0002 12.5507 10.1961 13.0216 10.5877 13.4132C10.9794 13.8049 11.4502 14.0007 12.0002 14.0007Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconMyLocation;
