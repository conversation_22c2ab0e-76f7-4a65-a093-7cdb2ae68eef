import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconParticipate = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-participate ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_3852_190637"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_3852_190637)">
          <path
            d="M11.0016 21.5004C9.68772 21.5004 8.45268 21.2511 7.29655 20.7524C6.1404 20.2538 5.13472 19.577 4.2795 18.7222C3.42427 17.8673 2.74721 16.8621 2.24833 15.7065C1.74944 14.5508 1.5 13.3161 1.5 12.0021C1.5 10.6882 1.74937 9.45317 2.24812 8.29704C2.74688 7.14089 3.42375 6.13521 4.27875 5.27999C5.13375 4.42476 6.13917 3.7477 7.295 3.24881C8.45082 2.74993 9.68581 2.50049 11 2.50049C11.691 2.50049 12.3602 2.56972 13.0077 2.70819C13.6551 2.84666 14.2743 3.04922 14.8654 3.31589V4.99854C14.2948 4.68444 13.6845 4.43956 13.0345 4.26391C12.3844 4.08828 11.7062 4.00046 11 4.00046C8.78331 4.00046 6.89581 4.77963 5.33748 6.33796C3.77914 7.8963 2.99998 9.7838 2.99998 12.0005C2.99998 14.2171 3.77914 16.1046 5.33748 17.663C6.89581 19.2213 8.78331 20.0005 11 20.0005C13.2166 20.0005 15.1041 19.2213 16.6625 17.663C18.2208 16.1046 19 14.2171 19 12.0005C19 11.4928 18.9522 11.0002 18.8567 10.5226C18.7612 10.045 18.6282 9.58252 18.4577 9.13509H20.0692C20.2128 9.59277 20.3205 10.0571 20.3923 10.5279C20.4641 10.9988 20.5 11.4897 20.5 12.0005C20.5 13.3146 20.2506 14.5496 19.752 15.7054C19.2533 16.8613 18.5765 17.8667 17.7217 18.7217C16.8669 19.5767 15.8616 20.2536 14.706 20.7523C13.5504 21.2511 12.3156 21.5004 11.0016 21.5004ZM19.25 6.75044V4.75044H17.25V3.25049H19.25V1.25049H20.75V3.25049H22.75V4.75044H20.75V6.75044H19.25ZM14.4061 10.8081C14.7699 10.8081 15.0785 10.6808 15.3317 10.4261C15.5849 10.1714 15.7115 9.86207 15.7115 9.49819C15.7115 9.13432 15.5841 8.82579 15.3294 8.57259C15.0747 8.31939 14.7654 8.19279 14.4016 8.19279C14.0377 8.19279 13.7292 8.32015 13.476 8.57486C13.2227 8.82957 13.0961 9.13886 13.0961 9.50274C13.0961 9.86661 13.2235 10.1751 13.4782 10.4283C13.7329 10.6815 14.0422 10.8081 14.4061 10.8081ZM7.5984 10.8081C7.96227 10.8081 8.2708 10.6808 8.524 10.4261C8.77722 10.1714 8.90383 9.86207 8.90383 9.49819C8.90383 9.13432 8.77647 8.82579 8.52175 8.57259C8.26703 8.31939 7.95774 8.19279 7.59388 8.19279C7.23001 8.19279 6.92147 8.32015 6.66825 8.57486C6.41505 8.82957 6.28845 9.13886 6.28845 9.50274C6.28845 9.86661 6.41581 10.1751 6.67053 10.4283C6.92524 10.6815 7.23453 10.8081 7.5984 10.8081ZM11 17.1927C12.0529 17.1927 13.0091 16.9008 13.8685 16.3168C14.7279 15.7328 15.3628 14.9607 15.773 14.0005H6.22693C6.63718 14.9607 7.27201 15.7328 8.13143 16.3168C8.99083 16.9008 9.94701 17.1927 11 17.1927Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconParticipate;
