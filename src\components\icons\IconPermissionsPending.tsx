import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconPermissionsPending = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-permissions-pending ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_7075_168279"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_7075_168279)">
          <path
            d="M12 15.7505C13.1805 15.7505 14.184 15.3375 15.0104 14.5113C15.8368 13.6851 16.25 12.6819 16.25 11.5017C16.25 10.3215 15.8368 9.31788 15.0104 8.49096C14.184 7.66405 13.1805 7.25059 12 7.25059V8.44286C12.8525 8.44286 13.5753 8.74014 14.1683 9.33469C14.7612 9.92924 15.0577 10.6512 15.0577 11.5006C15.0577 12.3499 14.7604 13.0719 14.1659 13.6664C13.5713 14.261 12.8493 14.5583 12 14.5583C11.4617 14.5583 10.9602 14.423 10.4953 14.1525C10.0305 13.882 9.6551 13.5179 9.3692 13.0602L8.3577 13.6698C8.75128 14.3134 9.26693 14.8211 9.90465 15.1929C10.5424 15.5646 11.2408 15.7505 12 15.7505ZM8.34613 12.4179C8.54997 12.4179 8.72273 12.3471 8.8644 12.2054C9.00607 12.0637 9.0769 11.891 9.0769 11.6871C9.0769 11.4833 9.00607 11.3105 8.8644 11.1688C8.72273 11.0272 8.54837 10.9563 8.34132 10.9563C8.13427 10.9563 7.96152 11.0253 7.82305 11.1631C7.6846 11.301 7.61537 11.4754 7.61537 11.6862C7.61537 11.8971 7.68439 12.0717 7.82243 12.2102C7.96046 12.3487 8.13503 12.4179 8.34613 12.4179ZM8.8673 10.3275C9.07113 10.3275 9.24388 10.2583 9.38555 10.1198C9.52722 9.98134 9.59805 9.80859 9.59805 9.60154C9.59805 9.39449 9.52903 9.22013 9.391 9.07846C9.25297 8.9368 9.0784 8.86596 8.8673 8.86596C8.65618 8.86596 8.48161 8.9368 8.34357 9.07846C8.20554 9.22013 8.13652 9.39449 8.13652 9.60154C8.13652 9.80859 8.20736 9.98134 8.34903 10.1198C8.49069 10.2583 8.66345 10.3275 8.8673 10.3275ZM10.298 8.93326C10.5019 8.93326 10.6747 8.86243 10.8163 8.72076C10.958 8.5791 11.0288 8.40634 11.0288 8.20249C11.0288 7.99864 10.958 7.82588 10.8163 7.68421C10.6747 7.54255 10.5003 7.47171 10.2932 7.47171C10.0862 7.47171 9.91344 7.54065 9.77497 7.67851C9.63652 7.81638 9.5673 7.99074 9.5673 8.20159C9.5673 8.41246 9.63632 8.58711 9.77435 8.72556C9.91238 8.86403 10.0869 8.93326 10.298 8.93326ZM12 21.4813C9.83716 20.8916 8.04646 19.6185 6.62787 17.6621C5.20929 15.7057 4.5 13.5185 4.5 11.1006V5.34674L12 2.53906L19.5 5.34674V11.1006C19.5 13.5185 18.7907 15.7057 17.3721 17.6621C15.9535 19.6185 14.1628 20.8916 12 21.4813ZM12 19.9006C13.7333 19.3506 15.1666 18.2506 16.3 16.6006C17.4333 14.9506 18 13.1172 18 11.1006V6.37556L12 4.13519L5.99997 6.37556V11.1006C5.99997 13.1172 6.56664 14.9506 7.69997 16.6006C8.83331 18.2506 10.2666 19.3506 12 19.9006Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconPermissionsPending;
