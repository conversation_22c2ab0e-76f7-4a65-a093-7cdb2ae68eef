import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconPersonPlaceholder = ({
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-person_placeholder ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="49"
        height="49"
        viewBox="0 0 49 49"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_3237_135930)">
          <path
            d="M33.7329 20.457C33.7329 25.5562 29.5992 29.6899 24.5 29.6899C19.4008 29.6899 15.2671 25.5562 15.2671 20.457C15.2671 15.3578 19.4008 11.2241 24.5 11.2241C29.5992 11.2241 33.7329 15.3578 33.7329 20.457Z"
            fill="#FEFEFE"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M24.5 27.8899C28.6051 27.8899 31.9329 24.5621 31.9329 20.457C31.9329 16.3519 28.6051 13.0241 24.5 13.0241C20.3949 13.0241 17.0671 16.3519 17.0671 20.457C17.0671 24.5621 20.3949 27.8899 24.5 27.8899ZM24.5 29.6899C29.5992 29.6899 33.7329 25.5562 33.7329 20.457C33.7329 15.3578 29.5992 11.2241 24.5 11.2241C19.4008 11.2241 15.2671 15.3578 15.2671 20.457C15.2671 25.5562 19.4008 29.6899 24.5 29.6899Z"
            fill="#AAAAAA"
          />
          <path
            d="M46.0999 53.8559C46.0999 65.7852 36.4293 75.4559 24.4999 75.4559C12.5706 75.4559 2.8999 65.7852 2.8999 53.8559C2.8999 41.9265 12.5706 32.2559 24.4999 32.2559C36.4293 32.2559 46.0999 41.9265 46.0999 53.8559Z"
            fill="#FEFEFE"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M24.4999 73.6559C35.4352 73.6559 44.2999 64.7911 44.2999 53.8559C44.2999 42.9206 35.4352 34.0559 24.4999 34.0559C13.5647 34.0559 4.6999 42.9206 4.6999 53.8559C4.6999 64.7911 13.5647 73.6559 24.4999 73.6559ZM24.4999 75.4559C36.4293 75.4559 46.0999 65.7852 46.0999 53.8559C46.0999 41.9265 36.4293 32.2559 24.4999 32.2559C12.5706 32.2559 2.8999 41.9265 2.8999 53.8559C2.8999 65.7852 12.5706 75.4559 24.4999 75.4559Z"
            fill="#AAAAAA"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M24.5 46.5005C36.6503 46.5005 46.5 36.6508 46.5 24.5005C46.5 12.3502 36.6503 2.50049 24.5 2.50049C12.3497 2.50049 2.5 12.3502 2.5 24.5005C2.5 36.6508 12.3497 46.5005 24.5 46.5005ZM24.5 48.5005C37.7549 48.5005 48.5 37.7553 48.5 24.5005C48.5 11.2457 37.7549 0.500488 24.5 0.500488C11.2452 0.500488 0.5 11.2457 0.5 24.5005C0.5 37.7553 11.2452 48.5005 24.5 48.5005Z"
            fill="#AAAAAA"
          />
        </g>
        <defs>
          <clipPath id="clip0_3237_135930">
            <path
              d="M0.5 24.5005C0.5 11.2457 11.2452 0.500488 24.5 0.500488C37.7548 0.500488 48.5 11.2457 48.5 24.5005C48.5 37.7553 37.7548 48.5005 24.5 48.5005C11.2452 48.5005 0.5 37.7553 0.5 24.5005Z"
              fill="white"
            />
          </clipPath>
        </defs>
      </svg>
    </SvgIcon>
  );
};

export default IconPersonPlaceholder;
