import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconPersonalDay = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-personal-day ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_2799_54674"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_2799_54674)">
          <path
            d="M11.2501 5.21181V1.51953H12.75V5.21181H11.2501ZM17.3231 7.73103L16.2693 6.66758L18.8827 4.03876L19.9366 5.11758L17.3231 7.73103ZM18.7885 12.7502V11.2503H22.4808V12.7502H18.7885ZM18.8827 19.9368L16.2693 17.3233L17.3328 16.2599L19.9616 18.8579L18.8827 19.9368ZM6.68665 7.74063L4.05395 5.10796L5.13277 4.04838L7.74045 6.68683L6.68665 7.74063ZM6.28855 19.058H10.5962C11.077 19.058 11.4889 18.8865 11.8318 18.5435C12.1748 18.2006 12.3462 17.7887 12.3462 17.3079C12.3462 16.8272 12.1805 16.4169 11.8491 16.0772C11.5177 15.7374 11.1116 15.5675 10.6308 15.5675H9.5097L9.07702 14.531C8.83087 13.9554 8.44657 13.4986 7.92412 13.1608C7.40169 12.823 6.82445 12.6541 6.1924 12.6541C5.31418 12.6541 4.56898 12.9602 3.9568 13.5724C3.34463 14.1845 3.03855 14.9297 3.03855 15.8079C3.03855 16.7118 3.35425 17.4794 3.98565 18.1108C4.61707 18.7422 5.3847 19.058 6.28855 19.058ZM6.28855 20.5579C4.97573 20.5579 3.85587 20.0944 2.92895 19.1675C2.00203 18.2406 1.53857 17.1207 1.53857 15.8079C1.53857 14.5143 1.99082 13.4153 2.8953 12.5108C3.79978 11.6063 4.89882 11.1541 6.1924 11.1541C7.14752 11.1541 8.01674 11.4185 8.80007 11.9474C9.58341 12.4762 10.1526 13.183 10.5077 14.0676C11.4103 14.0676 12.1908 14.373 12.8491 14.9839C13.5074 15.5948 13.8398 16.397 13.8462 17.3906C13.8257 18.2765 13.502 19.0259 12.8751 19.6387C12.2482 20.2515 11.4885 20.5579 10.5962 20.5579H6.28855ZM13.827 17.1983C13.7629 16.9483 13.6988 16.705 13.6347 16.4685C13.5706 16.232 13.5065 15.9887 13.4424 15.7387C14.2116 15.4156 14.8302 14.9192 15.2981 14.2493C15.7661 13.5794 16.0001 12.8297 16.0001 12.0003C16.0001 10.9003 15.6084 9.95859 14.8251 9.17526C14.0417 8.39192 13.1001 8.00026 12.0001 8.00026C10.9808 8.00026 10.093 8.33647 9.3366 9.00891C8.5802 9.68136 8.14815 10.5272 8.04045 11.5464C7.79047 11.4823 7.53535 11.4233 7.2751 11.3695C7.01485 11.3157 6.75972 11.2567 6.50972 11.1926C6.70459 9.82848 7.32478 8.70541 8.3703 7.82336C9.4158 6.94131 10.6257 6.50028 12.0001 6.50028C13.5257 6.50028 14.8238 7.03554 15.8943 8.10606C16.9648 9.17656 17.5001 10.4746 17.5001 12.0003C17.5001 13.1746 17.1638 14.2326 16.4914 15.1743C15.819 16.1159 14.9308 16.7906 13.827 17.1983Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconPersonalDay;
