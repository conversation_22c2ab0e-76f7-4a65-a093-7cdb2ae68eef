import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconPublicTransport = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-public-transport ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_4944_2984517"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_4944_2984517)">
          <path
            d="M6.51925 20.5004C6.30675 20.5004 6.12862 20.4286 5.98487 20.2848C5.84112 20.1411 5.76925 19.9629 5.76925 19.7504V17.7774C5.45643 17.5402 5.16669 17.2046 4.90002 16.7706C4.63334 16.3367 4.5 15.8492 4.5 15.3081V6.00046C4.5 4.79006 5.10154 3.90414 6.30462 3.34269C7.50769 2.78122 9.40603 2.50049 11.9997 2.50049C14.687 2.50049 16.6089 2.77028 17.7653 3.30986C18.9217 3.84945 19.5 4.74631 19.5 6.00046V15.3081C19.5 15.8492 19.3666 16.3367 19.0999 16.7706C18.8333 17.2046 18.5435 17.5402 18.2307 17.7774V19.7504C18.2307 19.9629 18.1588 20.1411 18.0151 20.2848C17.8713 20.4286 17.6932 20.5004 17.4807 20.5004H16.8653C16.6529 20.5004 16.4747 20.4286 16.331 20.2848C16.1872 20.1411 16.1154 19.9629 16.1154 19.7504V18.5004H7.88457V19.7504C7.88457 19.9629 7.8127 20.1411 7.66895 20.2848C7.52522 20.4286 7.3471 20.5004 7.1346 20.5004H6.51925ZM5.99997 10.5005H18V6.76969H5.99997V10.5005ZM8.50225 15.8081C8.86612 15.8081 9.17465 15.6808 9.42785 15.4261C9.68105 15.1714 9.80765 14.8621 9.80765 14.4982C9.80765 14.1343 9.68029 13.8258 9.42557 13.5726C9.17087 13.3194 8.86158 13.1928 8.4977 13.1928C8.13383 13.1928 7.8253 13.3201 7.5721 13.5749C7.3189 13.8296 7.1923 14.1389 7.1923 14.5027C7.1923 14.8666 7.31966 15.1751 7.57438 15.4283C7.82908 15.6815 8.13837 15.8081 8.50225 15.8081ZM15.5022 15.8081C15.8661 15.8081 16.1747 15.6808 16.4278 15.4261C16.681 15.1714 16.8077 14.8621 16.8077 14.4982C16.8077 14.1343 16.6803 13.8258 16.4256 13.5726C16.1709 13.3194 15.8616 13.1928 15.4977 13.1928C15.1338 13.1928 14.8253 13.3201 14.5721 13.5749C14.3189 13.8296 14.1923 14.1389 14.1923 14.5027C14.1923 14.8666 14.3197 15.1751 14.5744 15.4283C14.8291 15.6815 15.1384 15.8081 15.5022 15.8081ZM6.27687 5.26971H17.7846C17.5731 4.87098 17.0372 4.55976 16.1769 4.33604C15.3167 4.11232 13.9346 4.00046 12.0307 4.00046C10.1577 4.00046 8.79099 4.11746 7.93073 4.35144C7.07048 4.58541 6.51919 4.8915 6.27687 5.26971ZM7.99997 17.0005H16C16.55 17.0005 17.0208 16.8046 17.4125 16.413C17.8041 16.0213 18 15.5505 18 15.0005V12.0005H5.99997V15.0005C5.99997 15.5505 6.19581 16.0213 6.58747 16.413C6.97914 16.8046 7.44998 17.0005 7.99997 17.0005Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconPublicTransport;
