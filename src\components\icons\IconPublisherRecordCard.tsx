import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconPublisherRecordCard = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-publisher-record-card ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_3478_158914"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_3478_158914)">
          <path
            d="M2.3077 20.3082C1.80257 20.3082 1.375 20.1332 1.025 19.7832C0.675 19.4332 0.5 19.0056 0.5 18.5005V5.50055C0.5 4.99543 0.675 4.56787 1.025 4.21787C1.375 3.86787 1.80257 3.69287 2.3077 3.69287H21.6922C22.1974 3.69287 22.6249 3.86787 22.9749 4.21787C23.3249 4.56787 23.4999 4.99543 23.4999 5.50055V18.5005C23.4999 19.0056 23.3249 19.4332 22.9749 19.7832C22.6249 20.1332 22.1974 20.3082 21.6922 20.3082H2.3077ZM15.3422 18.8082H21.6923C21.7692 18.8082 21.8397 18.7761 21.9038 18.712C21.9679 18.6479 22 18.5774 22 18.5005V5.50055C22 5.42363 21.9679 5.35311 21.9038 5.289C21.8397 5.2249 21.7692 5.19285 21.6923 5.19285H2.3077C2.23077 5.19285 2.16024 5.2249 2.09613 5.289C2.03202 5.35311 1.99998 5.42363 1.99998 5.50055V18.5005C1.99998 18.5774 2.03202 18.6479 2.09613 18.712C2.16024 18.7761 2.23077 18.8082 2.3077 18.8082H2.6577C3.3577 17.7313 4.26668 16.8787 5.38463 16.2505C6.50256 15.6223 7.70767 15.3082 8.99998 15.3082C10.2923 15.3082 11.4974 15.6223 12.6153 16.2505C13.7333 16.8787 14.6423 17.7313 15.3422 18.8082ZM8.99998 14.1159C9.76279 14.1159 10.4118 13.8483 10.9471 13.313C11.4823 12.7778 11.7499 12.1287 11.7499 11.3659C11.7499 10.6031 11.4823 9.95409 10.9471 9.41882C10.4118 8.88357 9.76279 8.61595 8.99998 8.61595C8.23716 8.61595 7.58813 8.88357 7.05288 9.41882C6.51762 9.95409 6.25 10.6031 6.25 11.3659C6.25 12.1287 6.51762 12.7778 7.05288 13.313C7.58813 13.8483 8.23716 14.1159 8.99998 14.1159ZM4.54997 18.8082H13.45C12.8769 18.1749 12.2045 17.6832 11.4327 17.3332C10.6609 16.9832 9.84998 16.8082 8.99998 16.8082C8.14998 16.8082 7.34164 16.9832 6.57498 17.3332C5.80831 17.6832 5.13331 18.1749 4.54997 18.8082ZM8.99998 12.6159C8.65254 12.6159 8.35734 12.4945 8.11437 12.2515C7.87142 12.0086 7.74995 11.7134 7.74995 11.3659C7.74995 11.0185 7.87142 10.7233 8.11437 10.4803C8.35734 10.2374 8.65254 10.1159 8.99998 10.1159C9.34741 10.1159 9.64261 10.2374 9.88558 10.4803C10.1285 10.7233 10.25 11.0185 10.25 11.3659C10.25 11.7134 10.1285 12.0086 9.88558 12.2515C9.64261 12.4945 9.34741 12.6159 8.99998 12.6159Z"
            fill={color}
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M18.0409 11.9335L17.0488 11.9335L17.0488 9.57167L18.0409 9.57167L18.0409 11.9335ZM16.3004 12.8321C16.6893 12.9999 17.1056 13.0838 17.5492 13.0838C17.9929 13.0838 18.4092 12.9999 18.7981 12.8321C19.187 12.6643 19.5252 12.4365 19.8129 12.1489C20.1006 11.8612 20.3283 11.5229 20.4962 11.134C20.664 10.7451 20.7479 10.3288 20.7479 9.88517C20.7479 9.44151 20.664 9.02523 20.4962 8.63633C20.3283 8.24744 20.1006 7.90915 19.8129 7.62147C19.5252 7.3338 19.187 7.10605 18.7981 6.93824C18.4092 6.77043 17.9929 6.68652 17.5492 6.68652C17.1056 6.68652 16.6893 6.77043 16.3004 6.93824C15.9115 7.10605 15.5732 7.3338 15.2855 7.62147C14.9979 7.90915 14.7701 8.24744 14.6023 8.63633C14.4345 9.02523 14.3506 9.44151 14.3506 9.88517C14.3506 10.3288 14.4345 10.7451 14.6023 11.134C14.7701 11.5229 14.9979 11.8612 15.2855 12.1489C15.5732 12.4365 15.9115 12.6643 16.3004 12.8321ZM17.5504 8.99472C17.8867 8.99472 18.1594 8.72206 18.1594 8.38571C18.1594 8.04937 17.8867 7.7767 17.5504 7.7767C17.214 7.7767 16.9413 8.04937 16.9413 8.38571C16.9413 8.72206 17.214 8.99472 17.5504 8.99472Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconPublisherRecordCard;
