import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconPublishersReports = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-publishers-reports ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_2515_23851"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_2515_23851)">
          <path
            d="M0.524678 4.42373C0.431095 3.92501 0.528211 3.47565 0.816028 3.07565C1.10384 2.67565 1.49711 2.42886 1.99583 2.33528L12.2304 0.525654C12.7291 0.432071 13.1785 0.529189 13.5785 0.817006C13.9785 1.10482 14.2253 1.49809 14.3189 1.9968L14.5496 3.30836H13.0189L12.8247 2.21218C12.8118 2.14166 12.7734 2.08557 12.7093 2.04391C12.6452 2.00224 12.5746 1.98781 12.4977 2.00063L2.25928 3.81985C2.16953 3.83267 2.10222 3.87434 2.05735 3.94485C2.01247 4.01537 1.99644 4.0955 2.00928 4.18525L3.30738 11.5181V15.9737C3.05994 15.8429 2.8484 15.6637 2.67275 15.4362C2.49712 15.2086 2.3843 14.9493 2.3343 14.6583L0.524678 4.42373Z"
            fill={color}
          />
          <path
            d="M7.62395 20.6855H16.3507V15.6656H21.3705V6.93889C21.3705 6.84879 21.3416 6.77477 21.2836 6.71684C21.2257 6.65891 21.1517 6.62995 21.0616 6.62995H7.62395C7.53385 6.62995 7.45983 6.65891 7.4019 6.71684C7.34397 6.77477 7.31501 6.84879 7.31501 6.93889V20.3765C7.31501 20.4666 7.34397 20.5407 7.4019 20.5986C7.45983 20.6565 7.53385 20.6855 7.62395 20.6855ZM7.62395 22.1914C7.12326 22.1914 6.69561 22.0141 6.34101 21.6595C5.98639 21.3049 5.80908 20.8772 5.80908 20.3765V6.93889C5.80908 6.4382 5.98639 6.01055 6.34101 5.65595C6.69561 5.30133 7.12326 5.12402 7.62395 5.12402H21.0616C21.5623 5.12402 21.9899 5.30133 22.3445 5.65595C22.6992 6.01055 22.8765 6.4382 22.8765 6.93889V16.3124L16.9975 22.1914H7.62395ZM9.76704 15.3953V13.8894H14.3428V15.3953H9.76704ZM9.76704 11.3988V9.89287H18.9185V11.3988H9.76704Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconPublishersReports;
