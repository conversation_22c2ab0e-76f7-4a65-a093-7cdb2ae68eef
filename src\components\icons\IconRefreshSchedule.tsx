import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconRefreshSchedule = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-refresh-schedule ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_3175_70025"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_3175_70025)">
          <path
            d="M5.3077 21.5006C4.80257 21.5006 4.375 21.3256 4.025 20.9756C3.675 20.6256 3.5 20.198 3.5 19.6929V6.30833C3.5 5.8032 3.675 5.37563 4.025 5.02563C4.375 4.67563 4.80257 4.50063 5.3077 4.50063H6.69232V2.38525H8.23075V4.50063H15.8077V2.38525H17.3076V4.50063H18.6922C19.1974 4.50063 19.625 4.67563 19.975 5.02563C20.325 5.37563 20.5 5.8032 20.5 6.30833V12.1545H19V10.3083H4.99997V19.6929C4.99997 19.7698 5.03202 19.8403 5.09612 19.9045C5.16024 19.9686 5.23077 20.0006 5.3077 20.0006H12.1442V21.5006H5.3077ZM19 23.8468C17.8602 23.8468 16.857 23.4964 15.9904 22.7958C15.1237 22.0952 14.5609 21.2147 14.3019 20.1545H15.5442C15.7865 20.8942 16.2182 21.4958 16.8394 21.9593C17.4606 22.4227 18.1807 22.6545 19 22.6545C20.0115 22.6545 20.8734 22.2984 21.5856 21.5862C22.2977 20.874 22.6538 20.0121 22.6538 19.0006C22.6538 17.9891 22.2977 17.1272 21.5856 16.415C20.8734 15.7028 20.0115 15.3468 19 15.3468C18.4525 15.3468 17.9352 15.4647 17.448 15.7006C16.9608 15.9365 16.5365 16.2545 16.175 16.6545H17.8461V17.8468H14.1538V14.1545H15.3461V15.791C15.8089 15.3 16.3564 14.9045 16.9884 14.6045C17.6205 14.3045 18.291 14.1545 19 14.1545C20.3384 14.1545 21.4807 14.6275 22.4269 15.5737C23.3731 16.5198 23.8461 17.6622 23.8461 19.0006C23.8461 20.3391 23.3731 21.4814 22.4269 22.4275C21.4807 23.3737 20.3384 23.8468 19 23.8468ZM4.99997 8.80835H19V6.30833C19 6.2314 18.9679 6.16087 18.9038 6.09675C18.8397 6.03265 18.7692 6.0006 18.6922 6.0006H5.3077C5.23077 6.0006 5.16024 6.03265 5.09612 6.09675C5.03202 6.16087 4.99997 6.2314 4.99997 6.30833V8.80835Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconRefreshSchedule;
