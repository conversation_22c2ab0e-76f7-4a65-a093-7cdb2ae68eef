import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconReportToBranch = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-report-to-branch ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_2513_2798"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_2513_2798)">
          <path
            d="M7.23576 14.8921H9.86266L14.9973 9.7575C15.1152 9.63315 15.2037 9.49597 15.2627 9.34597C15.3216 9.19597 15.3511 9.04854 15.3511 8.90367C15.3511 8.75881 15.3191 8.6165 15.255 8.47675C15.1909 8.33702 15.105 8.20497 14.9973 8.0806L14.0877 7.1306C13.9697 7.01267 13.8367 6.92421 13.6886 6.86522C13.5405 6.80626 13.3889 6.77677 13.2338 6.77677C13.089 6.77677 12.9415 6.80626 12.7915 6.86522C12.6415 6.92421 12.5044 7.01267 12.38 7.1306L7.23576 12.2652V14.8921ZM8.42803 13.6998V12.7498L11.4146 9.76327L11.8954 10.2325L12.355 10.7229L9.37803 13.6998H8.42803ZM11.8954 10.2325L12.355 10.7229L11.4146 9.76327L11.8954 10.2325ZM12.5358 14.8921H18.8511V13.3922H14.0357L12.5358 14.8921ZM3.54346 22.1228V5.39217C3.54346 4.88704 3.71846 4.45947 4.06846 4.10947C4.41846 3.75947 4.84602 3.58447 5.35116 3.58447H20.7357C21.2408 3.58447 21.6684 3.75947 22.0184 4.10947C22.3684 4.45947 22.5434 4.88704 22.5434 5.39217V16.7767C22.5434 17.2819 22.3684 17.7094 22.0184 18.0594C21.6684 18.4094 21.2408 18.5844 20.7357 18.5844H7.08191L3.54346 22.1228ZM6.44346 17.0844H20.7357C20.8126 17.0844 20.8832 17.0524 20.9473 16.9883C21.0114 16.9242 21.0434 16.8537 21.0434 16.7767V5.39217C21.0434 5.31524 21.0114 5.24472 20.9473 5.1806C20.8832 5.1165 20.8126 5.08445 20.7357 5.08445H5.35116C5.27422 5.08445 5.2037 5.1165 5.13958 5.1806C5.07548 5.24472 5.04343 5.31524 5.04343 5.39217V18.4691L6.44346 17.0844Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconReportToBranch;
