import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconSettings = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-settings ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_2515_23653"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_2515_23653)">
          <path
            d="M9.69245 21.5004L9.31168 18.4543C9.04373 18.3645 8.76905 18.2389 8.48763 18.0774C8.20621 17.9158 7.9546 17.7427 7.7328 17.5581L4.91165 18.7504L2.604 14.7505L5.04438 12.9062C5.0213 12.7575 5.00495 12.6082 4.99533 12.4582C4.98571 12.3082 4.9809 12.1588 4.9809 12.0101C4.9809 11.8678 4.98571 11.7232 4.99533 11.5764C5.00495 11.4296 5.0213 11.269 5.04438 11.0947L2.604 9.25046L4.91165 5.26974L7.72318 6.45244C7.96421 6.26141 8.22159 6.08672 8.4953 5.92839C8.76904 5.77004 9.03795 5.64279 9.30205 5.54664L9.69245 2.50049H14.3078L14.6885 5.55626C14.9885 5.66523 15.26 5.79247 15.503 5.93799C15.7459 6.08351 15.9911 6.25499 16.2386 6.45244L19.0886 5.26974L21.3962 9.25046L18.9174 11.1235C18.9533 11.2851 18.9728 11.436 18.976 11.5764C18.9792 11.7168 18.9808 11.8581 18.9808 12.0005C18.9808 12.1364 18.9776 12.2745 18.9712 12.4149C18.9648 12.5553 18.9417 12.7159 18.902 12.8966L21.3616 14.7505L19.0539 18.7504L16.2386 17.5485C15.9911 17.7459 15.7386 17.9206 15.4809 18.0725C15.2232 18.2245 14.959 18.3485 14.6885 18.4447L14.3078 21.5004H9.69245ZM11.0001 20.0005H12.9655L13.3251 17.3216C13.8354 17.1883 14.3017 16.9989 14.7241 16.7533C15.1466 16.5078 15.5539 16.1921 15.9463 15.8062L18.4309 16.8505L19.4155 15.1505L17.2463 13.5158C17.3296 13.2569 17.3863 13.003 17.4165 12.7543C17.4466 12.5056 17.4616 12.2543 17.4616 12.0005C17.4616 11.7402 17.4466 11.4889 17.4165 11.2466C17.3863 11.0043 17.3296 10.7569 17.2463 10.5043L19.4347 8.85046L18.4501 7.15046L15.9366 8.21009C15.602 7.85239 15.2011 7.53636 14.7338 7.26201C14.2664 6.98765 13.7937 6.79341 13.3155 6.67931L13.0001 4.00046H11.0155L10.6847 6.66969C10.1745 6.79021 9.7033 6.97482 9.27125 7.22354C8.8392 7.47226 8.42703 7.79277 8.03473 8.18509L5.5501 7.15046L4.56548 8.85046L6.7251 10.4601C6.64177 10.6973 6.58344 10.944 6.5501 11.2005C6.51677 11.4569 6.5001 11.7268 6.5001 12.0101C6.5001 12.2703 6.51677 12.5255 6.5501 12.7755C6.58344 13.0255 6.63856 13.2723 6.71548 13.5158L4.56548 15.1505L5.5501 16.8505L8.0251 15.8005C8.40459 16.1902 8.81035 16.5094 9.2424 16.7582C9.67445 17.0069 10.152 17.1979 10.6751 17.3312L11.0001 20.0005ZM12.0116 15.0004C12.8437 15.0004 13.5517 14.7085 14.1357 14.1245C14.7196 13.5405 15.0116 12.8325 15.0116 12.0005C15.0116 11.1684 14.7196 10.4604 14.1357 9.87644C13.5517 9.29247 12.8437 9.00049 12.0116 9.00049C11.1693 9.00049 10.4588 9.29247 9.87993 9.87644C9.3011 10.4604 9.01168 11.1684 9.01168 12.0005C9.01168 12.8325 9.3011 13.5405 9.87993 14.1245C10.4588 14.7085 11.1693 15.0004 12.0116 15.0004Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconSettings;
