import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconShapes = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-shapes ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_4944_2979484"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_4944_2979484)">
          <path
            d="M8.38446 17.9695C8.50624 17.9798 8.62498 17.9875 8.74068 17.9926C8.85638 17.9977 8.98123 18.0003 9.11521 18.0003C9.25623 18.0003 9.38763 17.9977 9.50943 17.9926C9.63122 17.9875 9.75621 17.9798 9.88441 17.9695V19.5772C9.88441 19.6669 9.91326 19.7407 9.97096 19.7983C10.0287 19.856 10.1024 19.8849 10.1921 19.8849H19.5767C19.6665 19.8849 19.7402 19.856 19.7979 19.7983C19.8556 19.7407 19.8844 19.6669 19.8844 19.5772V10.1926C19.8844 10.1029 19.8556 10.0291 19.7979 9.97145C19.7402 9.91375 19.6665 9.8849 19.5767 9.8849H17.969C17.9793 9.7567 17.987 9.63171 17.9921 9.50992C17.9972 9.38812 17.9998 9.25671 17.9998 9.1157C17.9998 8.98172 17.9972 8.85687 17.9921 8.74117C17.987 8.62547 17.9793 8.50673 17.969 8.38495H19.5767C20.0738 8.38495 20.4994 8.56195 20.8534 8.91595C21.2074 9.26995 21.3844 9.69551 21.3844 10.1926V19.5772C21.3844 20.0743 21.2074 20.4999 20.8534 20.8539C20.4994 21.2079 20.0738 21.3849 19.5767 21.3849H10.1921C9.69502 21.3849 9.26946 21.2079 8.91546 20.8539C8.56146 20.4999 8.38446 20.0743 8.38446 19.5772V17.9695ZM9.11703 15.6156C7.30685 15.6156 5.77068 14.9852 4.50851 13.7242C3.24633 12.4633 2.61523 10.9277 2.61523 9.11752C2.61523 7.30734 3.24571 5.77116 4.50666 4.509C5.76761 3.24681 7.30318 2.61572 9.11336 2.61572C10.9236 2.61572 12.4597 3.2462 13.7219 4.50715C14.9841 5.7681 15.6152 7.30366 15.6152 9.11385C15.6152 10.924 14.9847 12.4602 13.7238 13.7224C12.4628 14.9846 10.9272 15.6156 9.11703 15.6156ZM9.11521 14.1157C10.4985 14.1157 11.6777 13.6282 12.6527 12.6532C13.6277 11.6782 14.1152 10.499 14.1152 9.1157C14.1152 7.73237 13.6277 6.5532 12.6527 5.5782C11.6777 4.6032 10.4985 4.1157 9.11521 4.1157C7.73188 4.1157 6.55271 4.6032 5.57771 5.5782C4.60271 6.5532 4.11521 7.73237 4.11521 9.1157C4.11521 10.499 4.60271 11.6782 5.57771 12.6532C6.55271 13.6282 7.73188 14.1157 9.11521 14.1157Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconShapes;
