import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconStadium = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-stadium ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_4944_2980565"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_4944_2980565)">
          <path
            d="M3.73086 7.0006V3.30833L7.42313 5.15445L3.73086 7.0006ZM17.5769 7.0006V3.30833L21.2692 5.15445L17.5769 7.0006ZM11.1154 6.07753V2.38525L14.8077 4.23138L11.1154 6.07753ZM10.75 21.6448C9.54104 21.6051 8.43079 21.5041 7.41926 21.3419C6.40772 21.1797 5.53305 20.9765 4.79523 20.7323C4.05742 20.4881 3.48082 20.2086 3.06543 19.8939C2.65007 19.5791 2.44238 19.2493 2.44238 18.9045V10.2891C2.44238 9.90448 2.68725 9.54743 3.17698 9.21793C3.66673 8.88845 4.33917 8.59903 5.19428 8.34968C6.04942 8.10031 7.05967 7.90288 8.22503 7.75738C9.39042 7.61186 10.6488 7.5391 12 7.5391C13.3513 7.5391 14.6097 7.61186 15.775 7.75738C16.9404 7.90288 17.9507 8.10031 18.8058 8.34968C19.6609 8.59903 20.3333 8.88845 20.8231 9.21793C21.3128 9.54743 21.5577 9.90448 21.5577 10.2891V18.9045C21.5577 19.2493 21.35 19.5791 20.9346 19.8939C20.5192 20.2086 19.9426 20.4881 19.2048 20.7323C18.467 20.9765 17.5923 21.1797 16.5808 21.3419C15.5693 21.5041 14.459 21.6051 13.2501 21.6448V17.6544H10.75V21.6448ZM12 11.5391C13.6359 11.5391 15.1087 11.4256 16.4183 11.1987C17.7279 10.9718 18.736 10.6885 19.4424 10.3487C19.3334 10.1307 18.6183 9.85798 17.2972 9.53043C15.976 9.20286 14.2103 9.03908 12 9.03908C9.78977 9.03908 8.02406 9.20286 6.70291 9.53043C5.38174 9.85798 4.66667 10.1307 4.55768 10.3487C5.2641 10.6885 6.23526 10.9718 7.47116 11.1987C8.70706 11.4256 10.2167 11.5391 12 11.5391ZM9.25006 20.0333V16.1545H14.75V20.0333C16.2051 19.9064 17.385 19.7041 18.2895 19.4266C19.1939 19.149 19.7834 18.8878 20.0577 18.6429V11.7237C19.0513 12.1609 17.8661 12.4894 16.502 12.7093C15.1379 12.9291 13.6372 13.0391 12 13.0391C10.3629 13.0391 8.86221 12.9291 7.49811 12.7093C6.13401 12.4894 4.94875 12.1609 3.94233 11.7237V18.6429C4.2167 18.8878 4.78689 19.149 5.65291 19.4266C6.51894 19.7041 7.71799 19.9064 9.25006 20.0333Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconStadium;
