import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconStatsYear = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-stats-year ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_2625_54819"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="25"
        >
          <rect y="0.000488281" width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_2625_54819)">
          <path
            d="M3 17.7504C2.5141 17.7504 2.10097 17.5802 1.7606 17.2399C1.4202 16.8995 1.25 16.4864 1.25 16.0005C1.25 15.5146 1.42019 15.1014 1.76057 14.7611C2.10096 14.4207 2.5141 14.2505 3 14.2505C3.11282 14.2505 3.21474 14.2553 3.30578 14.2649C3.39681 14.2745 3.48719 14.3056 3.57693 14.3582L8.35772 9.57739C8.30516 9.48766 8.27407 9.39727 8.26445 9.30624C8.25483 9.21521 8.25003 9.11328 8.25003 9.00046C8.25003 8.51456 8.42022 8.10143 8.7606 7.76106C9.10097 7.42068 9.5141 7.25049 10 7.25049C10.4859 7.25049 10.899 7.42068 11.2394 7.76106C11.5798 8.10143 11.75 8.51456 11.75 9.00046C11.75 9.05945 11.7205 9.24535 11.6615 9.55816L14.4423 12.3389C14.5321 12.2992 14.6192 12.2745 14.7038 12.2649C14.7885 12.2553 14.8872 12.2505 15 12.2505C15.1128 12.2505 15.2131 12.2553 15.301 12.2649C15.3888 12.2745 15.4744 12.3056 15.5577 12.3582L19.3577 8.55816C19.3052 8.47483 19.2741 8.38926 19.2645 8.30144C19.2548 8.21361 19.25 8.11328 19.25 8.00046C19.25 7.51456 19.4202 7.10143 19.7606 6.76106C20.101 6.42068 20.5141 6.25049 21 6.25049C21.4859 6.25049 21.899 6.42068 22.2394 6.76106C22.5798 7.10143 22.75 7.51456 22.75 8.00046C22.75 8.48636 22.5798 8.8995 22.2394 9.23986C21.899 9.58025 21.4859 9.75044 21 9.75044C20.8872 9.75044 20.7869 9.74563 20.699 9.73601C20.6112 9.7264 20.5256 9.69531 20.4423 9.64274L16.6423 13.4428C16.6948 13.5261 16.7259 13.6117 16.7355 13.6995C16.7452 13.7873 16.75 13.8876 16.75 14.0005C16.75 14.4864 16.5798 14.8995 16.2394 15.2399C15.899 15.5802 15.4859 15.7504 15 15.7504C14.5141 15.7504 14.101 15.5802 13.7606 15.2399C13.4202 14.8995 13.25 14.4864 13.25 14.0005C13.25 13.8876 13.2548 13.7857 13.2645 13.6947C13.2741 13.6037 13.3052 13.5133 13.3577 13.4235L10.5769 10.6427C10.4872 10.6953 10.3968 10.7264 10.3058 10.736C10.2147 10.7456 10.1128 10.7504 10 10.7504C9.94102 10.7504 9.75512 10.721 9.4423 10.662L4.66153 15.4428C4.70126 15.5325 4.72593 15.6197 4.73555 15.7043C4.74517 15.7889 4.74997 15.8876 4.74997 16.0005C4.74997 16.4864 4.57978 16.8995 4.2394 17.2399C3.89903 17.5802 3.4859 17.7504 3 17.7504Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconStatsYear;
