import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconSun = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-sun ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="14"
        height="14"
        viewBox="0 0 14 14"
        fill="none"
      >
        <mask
          id="mask0_2895_91279"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="14"
          height="14"
        >
          <rect width="14" height="14" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_2895_91279)">
          <path
            d="M6.99999 3.04006C6.75838 3.04006 6.56251 2.84419 6.56251 2.60257V1.32372C6.56251 1.0821 6.75838 0.88623 6.99999 0.88623C7.24161 0.88623 7.43748 1.0821 7.43748 1.32372V2.60257C7.43748 2.84419 7.24161 3.04006 6.99999 3.04006ZM10.4076 4.20711C10.2405 4.37417 9.96968 4.37417 9.80261 4.20711C9.63745 4.04195 9.6353 3.77484 9.79777 3.60704L10.6972 2.67804C10.8684 2.50129 11.1515 2.50002 11.3242 2.67523C11.4932 2.84672 11.4922 3.12248 11.322 3.29275L10.4076 4.20711ZM11.3974 7.43747C11.1558 7.43747 10.9599 7.2416 10.9599 6.99999C10.9599 6.75837 11.1558 6.5625 11.3974 6.5625H12.6763C12.9179 6.5625 13.1138 6.75837 13.1138 6.99999C13.1138 7.2416 12.9179 7.43747 12.6763 7.43747H11.3974ZM6.99999 13.1137C6.75838 13.1137 6.56251 12.9179 6.56251 12.6763V11.403C6.56251 11.1614 6.75838 10.9655 6.99999 10.9655C7.24161 10.9655 7.43748 11.1614 7.43748 11.403V12.6763C7.43748 12.9179 7.24161 13.1137 6.99999 13.1137ZM4.20307 4.19782C4.03749 4.35851 3.7743 4.35892 3.60823 4.19874L2.68213 3.30548C2.50372 3.13341 2.50346 2.84773 2.68153 2.67532C2.85318 2.50913 3.12632 2.51109 3.29557 2.67972L4.20707 3.58786C4.37641 3.75658 4.3746 4.03134 4.20307 4.19782ZM11.3201 11.3094C11.1457 11.492 10.8543 11.4925 10.6793 11.3106L9.79407 10.3909C9.63373 10.2243 9.63689 9.95984 9.80117 9.79712C9.96495 9.63489 10.2285 9.63371 10.3937 9.79446L11.3087 10.6846C11.4833 10.8544 11.4884 11.1333 11.3201 11.3094ZM1.32372 7.43747C1.0821 7.43747 0.88623 7.2416 0.88623 6.99999C0.88623 6.75837 1.0821 6.5625 1.32372 6.5625H2.60257C2.84419 6.5625 3.04006 6.75837 3.04006 6.99999C3.04006 7.2416 2.84419 7.43747 2.60257 7.43747H1.32372ZM3.30263 11.3267C3.12833 11.501 2.84486 11.498 2.67422 11.3201C2.50851 11.1474 2.51135 10.8739 2.6806 10.7046L3.5728 9.81245C3.74406 9.64119 4.02045 9.63715 4.19664 9.80334C4.37322 9.97194 4.37647 10.2528 4.20384 10.4255L3.30263 11.3267ZM7.00132 10.2083C6.11048 10.2083 5.35283 9.89651 4.72837 9.27293C4.1039 8.64935 3.79167 7.89214 3.79167 7.00131C3.79167 6.11048 4.10346 5.35283 4.72705 4.72837C5.35063 4.1039 6.10784 3.79167 6.99866 3.79167C7.8895 3.79167 8.64715 4.10346 9.27161 4.72704C9.89608 5.35063 10.2083 6.10783 10.2083 6.99866C10.2083 7.8895 9.89652 8.64715 9.27294 9.2716C8.64935 9.89607 7.89215 10.2083 7.00132 10.2083Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconSun;
