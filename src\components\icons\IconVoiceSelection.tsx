import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconVoiceSelection = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-voice-selection ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_13161_343504"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="24"
          height="24"
        >
          <rect width="24" height="24" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_13161_343504)">
          <path
            d="M2.5 21.4986V19.9986C3.29873 19.9986 4.08753 19.9518 4.86638 19.8583C5.64521 19.7647 6.40899 19.5749 7.15773 19.289C7.23464 19.257 7.2699 19.1977 7.2635 19.1111C7.25708 19.0246 7.19938 18.9685 7.0904 18.9428C6.31092 18.6364 5.68432 18.148 5.2106 17.4775C4.73687 16.807 4.5 16.0563 4.5 15.2256V13.3448H8.1923C8.28205 13.3448 8.35578 13.316 8.41348 13.2583C8.47116 13.2006 8.5 13.1269 8.5 13.0371V10.441H11.5288C11.6442 10.441 11.7324 10.3945 11.7933 10.3015C11.8542 10.2086 11.859 10.1108 11.8077 10.0083L8.7731 3.84289L10.1212 3.16406L13.1654 9.34866C13.4602 9.95121 13.4352 10.5339 13.0904 11.0967C12.7455 11.6595 12.2333 11.9409 11.5538 11.9409H9.99998V13.0948C9.99998 13.5807 9.82978 13.9938 9.4894 14.3342C9.14902 14.6746 8.73588 14.8448 8.25 14.8448H5.99998V15.2448C5.99998 15.8409 6.19197 16.3598 6.57595 16.8015C6.95992 17.2432 7.43972 17.539 8.01538 17.689L8.31538 17.764C8.91153 17.9179 9.24004 18.3089 9.30093 18.9371C9.36183 19.5653 9.10189 20.0204 8.52113 20.3025C7.58524 20.7627 6.60961 21.0771 5.59423 21.2457C4.57884 21.4143 3.54743 21.4986 2.5 21.4986ZM16.1173 18.8698L15.0481 17.816C15.4301 17.4339 15.726 16.9977 15.9356 16.5073C16.1452 16.0169 16.25 15.4954 16.25 14.9429C16.25 14.3903 16.1452 13.8688 15.9356 13.3784C15.726 12.8881 15.4301 12.4518 15.0481 12.0698L16.1173 11.0006C16.6314 11.5147 17.032 12.1099 17.3192 12.7861C17.6064 13.4624 17.75 14.1813 17.75 14.9429C17.75 15.7044 17.6064 16.4207 17.3192 17.0919C17.032 17.763 16.6314 18.3557 16.1173 18.8698ZM18.7307 21.4986L17.6615 20.4294C18.3846 19.7063 18.9455 18.8772 19.3442 17.9419C19.7429 17.0066 19.9423 16.007 19.9423 14.9429C19.9423 13.8685 19.7429 12.8663 19.3442 11.9361C18.9455 11.006 18.3846 10.1794 17.6615 9.45631L18.7307 8.38711C19.5923 9.24865 20.2596 10.239 20.7326 11.3583C21.2057 12.4775 21.4423 13.6724 21.4423 14.9429C21.4423 16.2031 21.2057 17.3954 20.7326 18.5198C20.2596 19.6441 19.5923 20.6371 18.7307 21.4986Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconVoiceSelection;
