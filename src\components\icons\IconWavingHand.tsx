import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconWavingHand = ({
  color = '#222222',
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-waving-hand ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="16"
        height="17"
        viewBox="0 0 16 17"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_11367_211115"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="16"
          height="17"
        >
          <rect y="0.6875" width="16" height="16" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_11367_211115)">
          <path
            d="M7.31407 8.43081L11.7871 3.95134C11.8905 3.84792 12.0076 3.79621 12.1384 3.79621C12.2692 3.79621 12.3863 3.84792 12.4897 3.95134C12.5931 4.05475 12.6448 4.17184 12.6448 4.30261C12.6448 4.43337 12.5931 4.55046 12.4897 4.65387L8.02687 9.12694L7.31407 8.43081ZM8.91278 10.0231L12.9089 6.01671C13.0123 5.9133 13.1311 5.86159 13.2653 5.86159C13.3995 5.86159 13.5183 5.9133 13.6217 6.01671C13.7251 6.12013 13.7769 6.23893 13.7769 6.37311C13.7769 6.5073 13.7251 6.6261 13.6217 6.72952L9.61917 10.7257L8.91278 10.0231ZM3.65765 13.0167C2.70637 12.0654 2.2318 10.9235 2.23393 9.59106C2.23608 8.25859 2.71492 7.11458 3.67047 6.15902L5.6256 4.20389L6.44867 5.03337C6.54781 5.13252 6.6273 5.23872 6.68713 5.35197C6.74696 5.46522 6.7961 5.58552 6.83457 5.71287L9.27558 3.26159C9.37901 3.15818 9.49781 3.10647 9.63198 3.10647C9.76617 3.10647 9.88497 3.15818 9.98838 3.26159C10.0918 3.36501 10.1435 3.48381 10.1435 3.61799C10.1435 3.75218 10.0918 3.87098 9.98838 3.97439L7.09865 6.86414L5.86788 8.09106L6.19097 8.41412C6.65934 8.8825 6.8867 9.44447 6.87302 10.1C6.85934 10.7556 6.6149 11.3209 6.13968 11.7962L6.07558 11.8603L5.36918 11.1577L5.42688 11.0936C5.70381 10.8167 5.85082 10.4872 5.86792 10.1051C5.88501 9.72308 5.75509 9.3936 5.47817 9.11667L4.87433 8.52311C4.75382 8.40258 4.69357 8.25921 4.69357 8.09297C4.69357 7.92674 4.75382 7.78337 4.87433 7.66286L5.72177 6.83209C5.87647 6.67739 5.95382 6.49128 5.95382 6.27376C5.95382 6.05622 5.87647 5.87011 5.72177 5.71541L5.6256 5.61924L4.37302 6.86542C3.6132 7.62524 3.23008 8.53336 3.22367 9.58977C3.21726 10.6462 3.59396 11.5543 4.35378 12.3141C5.11362 13.074 6.02666 13.4539 7.0929 13.4539C8.15913 13.4539 9.07217 13.074 9.832 12.3141L13.5974 8.53851C13.7008 8.4351 13.8196 8.38339 13.9538 8.38339C14.088 8.38339 14.2068 8.4351 14.3102 8.53851C14.4136 8.64193 14.4653 8.76073 14.4653 8.89491C14.4653 9.0291 14.4136 9.1479 14.3102 9.25131L10.5281 13.0167C9.57258 13.9722 8.4275 14.45 7.0929 14.45C5.75829 14.45 4.61321 13.9722 3.65765 13.0167ZM11.0897 15.6205L11.0833 14.6103C11.8679 14.6103 12.5373 14.3331 13.0916 13.7789C13.6459 13.2246 13.923 12.5551 13.923 11.7705L14.9333 11.7769C14.9333 12.841 14.5587 13.7476 13.8095 14.4968C13.0604 15.2459 12.1538 15.6205 11.0897 15.6205ZM1.06665 5.59747C1.06665 4.53338 1.44122 3.62677 2.19037 2.87762C2.93951 2.12848 3.84613 1.75391 4.91022 1.75391L4.91663 2.76414C4.13201 2.76414 3.46257 3.04128 2.9083 3.59556C2.35402 4.14982 2.07688 4.81927 2.07688 5.60389L1.06665 5.59747Z"
            fill={color}
          />
        </g>
      </svg>
    </SvgIcon>
  );
};

export default IconWavingHand;
