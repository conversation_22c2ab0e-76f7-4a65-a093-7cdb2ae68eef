import { SvgIcon, SxProps, Theme } from '@mui/material';

type IconProps = {
  color?: string;
  width?: number;
  height?: number;
  sx?: SxProps<Theme>;
  className?: string;
};

const IconYahoo = ({
  width = 24,
  height = 24,
  sx = {},
  className,
}: IconProps) => {
  return (
    <SvgIcon
      className={`organized-icon-yahoo ${className}`}
      sx={{ width: `${width}px`, height: `${height}px`, ...sx }}
    >
      <svg
        width="25"
        height="24"
        viewBox="0 0 25 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_2633_30348)">
          <path
            d="M10.4116 6.39954L7.63168 13.2693L4.94994 6.40516C4.91156 6.30681 4.84439 6.22235 4.75722 6.16281C4.67004 6.10327 4.56693 6.07143 4.46136 6.07145H0.745409C0.65941 6.07143 0.574727 6.09257 0.498821 6.13299C0.422915 6.17342 0.358117 6.23189 0.310138 6.30326C0.262158 6.37463 0.23247 6.45671 0.223688 6.54226C0.214907 6.62781 0.227302 6.71421 0.259782 6.79384L5.27567 19.1057L3.99465 22.2789C3.96254 22.3585 3.95045 22.4448 3.95945 22.5302C3.96844 22.6155 3.99824 22.6974 4.04624 22.7686C4.09424 22.8397 4.15896 22.898 4.23474 22.9383C4.31052 22.9787 4.39503 22.9998 4.48087 22.9998H8.08125C8.29495 22.9998 8.48737 22.8701 8.56747 22.6721L14.9894 6.79265C15.0216 6.71303 15.0337 6.62673 15.0247 6.54133C15.0158 6.45592 14.986 6.37402 14.938 6.30282C14.89 6.23161 14.8252 6.17328 14.7494 6.13294C14.6736 6.0926 14.5891 6.07149 14.5032 6.07145H10.8978C10.7933 6.07157 10.6912 6.10286 10.6046 6.1613C10.518 6.21974 10.4508 6.30269 10.4116 6.39954ZM19.2495 1.32779C19.2887 1.23095 19.3559 1.14802 19.4425 1.08962C19.5292 1.03123 19.6313 1.00002 19.7357 1H23.3414C23.4273 1.00003 23.5119 1.02115 23.5877 1.06149C23.6635 1.10183 23.7282 1.16016 23.7762 1.23136C23.8242 1.30257 23.854 1.38447 23.863 1.46987C23.872 1.55528 23.8598 1.64158 23.8276 1.7212L20.0183 11.1045C19.9791 11.2013 19.9119 11.2842 19.8253 11.3426C19.7386 11.401 19.6365 11.4323 19.5321 11.4323H15.9317C15.8458 11.4323 15.7613 11.4112 15.6855 11.3709C15.6096 11.3306 15.5449 11.2723 15.4969 11.2011C15.4489 11.1299 15.4191 11.048 15.4101 10.9627C15.4011 10.8773 15.4133 10.791 15.4455 10.7114L19.2495 1.32779Z"
            fill="#6001D1"
          />
          <path
            d="M16.2187 18.8601C17.8273 18.8601 19.1313 17.5561 19.1313 15.9475C19.1313 14.3389 17.8273 13.0349 16.2187 13.0349C14.6102 13.0349 13.3062 14.3389 13.3062 15.9475C13.3062 17.5561 14.6102 18.8601 16.2187 18.8601Z"
            fill="#6001D1"
          />
        </g>
        <defs>
          <clipPath id="clip0_2633_30348">
            <rect
              width="24"
              height="24"
              fill="white"
              transform="translate(0.043457)"
            />
          </clipPath>
        </defs>
      </svg>
    </SvgIcon>
  );
};

export default IconYahoo;
